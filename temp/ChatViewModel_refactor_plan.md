### ChatViewModel Refactor Plan — Towards a Modular, Use-Case-Centric Architecture

#### Goals
- Reduce ChatViewModel’s responsibilities and cognitive load.
- Introduce use-case classes that each represent one or more cohesive user scenarios.
- Centralize shared, observable UI state in a single state holder that use-cases can read/update.
- Keep Compose-friendly StateFlows and simple shortcut properties on the ViewModel for easy consumption by composables.
- Improve testability by isolating logic (thread building, streaming orchestration, API interactions, error emission).

---

### High-Level Architecture

- ChatViewModel (thin)
  - Delegates all work to injected use-cases and services.
  - Exposes read-only StateFlows that are actually owned by SharedChatState.
  - Provides convenience wrapper methods that forward to use-cases.
  - No heavy business logic, no direct API calls.

- SharedChatState (single source of truth)
  - Owns all StateFlows and their backing MutableStateFlows.
  - Encapsulates mutations through internal methods.
  - Provides computed/derived flows (e.g., displayedMessages) by composing lower-level flows and ThreadBuilder service.
  - Use-cases depend on this to read/write state.

- Use-Cases (feature-oriented)
  - Each encapsulates one or more closely related user flows.
  - Operate on SharedChatState + required APIs/services.
  - Implement orchestration and error handling.
  - Coroutine context injected via dispatcher(s).
  - Return lightweight results (Unit, Either-like, or update state directly).

- Supporting services/utilities
  - ThreadBuilder: buildThreadBranch, findLeafOfBranch.
  - StreamingCoordinator: orchestration for streaming lifecycle and partial updates.
  - ErrorNotifier: wraps EventBus + resource mapping for consistent error emission.
  - TimeProvider: wraps Clock for easy mocking in tests.

- DI (Koin)
  - Provide singletons/factories for state, services, and use-cases.
  - ViewModel requests the use-cases and state via DI.

---

### Current Responsibilities Mapped to Target Modules

- Shared state (StateFlows currently in ChatViewModel)
  - sessionState (lines 67–74)
  - currentBranchLeafId (75–83)
  - streamingAssistantMessage, streamingUserMessage (84–89)
  - displayedMessages (95–109) -> recompute using ThreadBuilder
  - inputContent (111–117)
  - replyTargetMessage (118–125)
  - editingMessage (126–132)
  - editingContent (133–139)
  - isSendingMessage (140–146)
  - retry/snackbar tracking (147–172)
  - clearSession (229–240) -> state-level reset method

- Use-cases
  - LoadSessionUseCase: loadSession (176–227), retry behavior in init (147–172)
  - SendMessageUseCase (non-streaming): sendMessage branch + handleNonStreamingMessage (251–279, 371–406)
  - SendMessageStreamingUseCase: handleStreamingMessage + streaming ChatStreamEvent handling (281–369, 307–366)
  - ReplyUseCase: startReplyTo, cancelReply (408–416, 418–423)
  - EditMessageUseCase: startEditing, updateEditingContent, saveEditing, cancelEditing (425–485, 487–493)
  - DeleteMessageUseCase: deleteMessage (495–518)
  - SwitchBranchUseCase: switchBranchToMessage (520–566)
  - SelectModelUseCase: selectModel (568–590)
  - SelectSettingsUseCase: selectSettings (592–614)
  - UpdateInputUseCase: updateInput (242–249)

- Services/utilities
  - ThreadBuilder: buildThreadBranch (653–688), findLeafOfBranch (616–651)
  - StreamingCoordinator: orchestrate streaming lifecycle and update SharedChatState
  - ErrorNotifier: standardize apiRequestError + EventBus (used in 202–226, 380–386, 349–358)
  - TimeProvider: wraps Clock.System (imports 23, usage at 340–341, 398–399, 474–475)
  - SettingsProvider: flag for streaming mode (line 268 TODO)

---

### Proposed Module/Class Sketches

Note: Names are suggestions; adjust to your naming conventions.

- Shared State
  - interface ChatState
    - Expose read-only StateFlow properties:
      - sessionState: StateFlow<UiState<ApiError, ChatSession>>
      - currentBranchLeafId: StateFlow<Long?>
      - displayedMessages: StateFlow<List<ChatMessage>>
      - inputContent, replyTargetMessage, editingMessage, editingContent, isSendingMessage
      - retry metadata if needed (lastFailedLoadEventId, lastAttemptedSessionId) as internal or separate
  - class SharedChatState(
      uiDispatcher: CoroutineDispatcher,
      threadBuilder: ThreadBuilder,
      timeProvider: TimeProvider
    ) : ChatState
    - Holds the MutableStateFlows
    - Builds displayedMessages by combining sessionState, currentBranchLeafId, streaming states, and using threadBuilder.buildThreadBranch
    - Internal mutation methods:
      - setSessionLoading/Error/Success
      - setCurrentLeafId, setStreamingStates, setInputContent, setReplyTarget, setEditing, setIsSending
      - updateSessionMessages, updateSessionLeafModelSettings, clearAllState
      - setRetryMetadata + consumeRetryIfMatches
    - Exposes only read-only flows outward

- Use-Cases
  - class LoadSessionUseCase(
      sessionApi: SessionApi,
      state: SharedChatState,
      errorNotifier: ErrorNotifier,
      uiDispatcher: CoroutineDispatcher
    )
    - fun execute(sessionId: Long?, forceReload: Boolean)
    - Handles retry wiring via ErrorNotifier subscription API or provide a small RetryCoordinator

  - class SendMessageUseCase(
      chatApi: ChatApi,
      state: SharedChatState,
      settingsProvider: SettingsProvider,
      streamingCoordinator: StreamingCoordinator,
      errorNotifier: ErrorNotifier,
      timeProvider: TimeProvider,
      uiDispatcher: CoroutineDispatcher
    )
    - fun execute()
    - Branch on settingsProvider.isStreamingEnabled:
      - streamingCoordinator.execute(currentSession, content, parentId)
      - or call non-streaming path:
        - chatApi.processNewMessage
        - update state (messages, leafId, clear reply/input)

  - class StreamingCoordinator(
      chatApi: ChatApi,
      state: SharedChatState,
      errorNotifier: ErrorNotifier,
      timeProvider: TimeProvider,
      uiDispatcher: CoroutineDispatcher
    )
    - suspend fun execute(currentSession: ChatSession, content: String, parentId: Long?)
    - Collect flow, handle:
      - UserMessageSaved: set temp streaming user, set leafId, clear input/reply
      - AssistantMessageStart/Delta/End: update temp assistant, and on End persist to session messages, leaf, updatedAt; clear temp
      - ErrorOccurred: clear temp and notify
      - StreamCompleted: log only

  - class ReplyUseCase(state: SharedChatState)
    - fun start(message: ChatMessage)
    - fun cancel()

  - class EditMessageUseCase(
      chatApi: ChatApi,
      state: SharedChatState,
      timeProvider: TimeProvider,
      errorNotifier: ErrorNotifier,
      uiDispatcher: CoroutineDispatcher
    )
    - fun start(message)
    - fun updateContent(text)
    - fun save()
    - fun cancel()

  - class DeleteMessageUseCase(
      chatApi: ChatApi,
      state: SharedChatState,
      uiDispatcher: CoroutineDispatcher
    )
    - fun execute(messageId: Long)
    - Option: optimistic UI, or reload via LoadSessionUseCase

  - class SwitchBranchUseCase(
      sessionApi: SessionApi,
      threadBuilder: ThreadBuilder,
      state: SharedChatState,
      uiDispatcher: CoroutineDispatcher
    )
    - fun execute(targetMessageId: Long)
    - Uses threadBuilder.findLeafOfBranch
    - Persist new leaf via API, then update state

  - class SelectModelUseCase(
      sessionApi: SessionApi,
      state: SharedChatState,
      uiDispatcher: CoroutineDispatcher
    )
    - fun execute(modelId: Long?)

  - class SelectSettingsUseCase(
      sessionApi: SessionApi,
      state: SharedChatState,
      uiDispatcher: CoroutineDispatcher
    )
    - fun execute(settingsId: Long?)

  - class UpdateInputUseCase(state: SharedChatState)
    - fun execute(newText: String)

- Services/Utilities
  - interface ThreadBuilder
    - fun buildThreadBranch(allMessages: List<ChatMessage>, leafId: Long?): List<ChatMessage>
    - fun findLeafOfBranch(startMessageId: Long, messageMap: Map<Long, ChatMessage>): Long?
  - class DefaultThreadBuilder : ThreadBuilder
    - Move logic from lines 616–651 and 661–688

  - class ErrorNotifier(
      eventBus: EventBus,
      resources: ResourcesFacade
    )
    - fun apiError(error: ApiError, shortMessageResId: Res.string, retryable: Boolean = false): String /* returns eventId */
    - Encapsulate apiRequestError + event emission; return eventId for retry tracking

  - interface TimeProvider { fun now(): Instant }
  - class SystemTimeProvider(private val clock: Clock) : TimeProvider { override fun now() = clock.now() }

  - interface SettingsProvider { val isStreamingEnabled: Boolean }
  - class ApiBackedSettingsProvider(settingsApi: SettingsApi) : SettingsProvider
    - or a simple in-memory for now with TODO to query SettingsApi

  - ResourcesFacade to wrap getString safely in shared code if needed.

---

### ViewModel After Refactor

- Constructor injects:
  - ChatState (SharedChatState)
  - Use-cases: loadSessionUC, sendMessageUC, replyUC, editMessageUC, deleteMessageUC, switchBranchUC, selectModelUC, selectSettingsUC, updateInputUC
  - Any additional coordinators if ViewModel must observe retry events

- Public properties (shortcuts):
  - val sessionState = state.sessionState
  - val displayedMessages = state.displayedMessages
  - val currentBranchLeafId = state.currentBranchLeafId
  - val inputContent = state.inputContent
  - val replyTargetMessage = state.replyTargetMessage
  - val editingMessage = state.editingMessage
  - val editingContent = state.editingContent
  - val isSendingMessage = state.isSendingMessage

- Public methods:
  - fun loadSession(id: Long?, forceReload: Boolean) = loadSessionUC.execute(id, forceReload)
  - fun clearSession() = state.clearAll()  // or via a ClearSessionUseCase if preferred
  - fun updateInput(text) = updateInputUC.execute(text)
  - fun sendMessage() = sendMessageUC.execute()
  - fun startReplyTo(msg) = replyUC.start(msg)
  - fun cancelReply() = replyUC.cancel()
  - fun startEditing(msg) = editMessageUC.start(msg)
  - fun updateEditingContent(text) = editMessageUC.updateContent(text)
  - fun saveEditing() = editMessageUC.save()
  - fun cancelEditing() = editMessageUC.cancel()
  - fun deleteMessage(id) = deleteMessageUC.execute(id)
  - fun switchBranchToMessage(id) = switchBranchUC.execute(id)
  - fun selectModel(modelId) = selectModelUC.execute(modelId)
  - fun selectSettings(settingsId) = selectSettingsUC.execute(settingsId)

- EventBus retry handling (lines 147–172):
  - Move to LoadSessionUseCase or a small RetryCoordinator that listens for SnackbarInteractionEvent, matches originalAppEventId, and re-invokes loadSession with lastAttemptedId.
  - SharedChatState can still store lastFailedLoadEventId and lastAttemptedSessionId for continuity.

---

### Koin Wiring (example outline)

- single<ThreadBuilder> { DefaultThreadBuilder() }
- single<TimeProvider> { SystemTimeProvider(Clock.System) }
- single<SettingsProvider> { ApiBackedSettingsProvider(get()) }
- single { ErrorNotifier(get(), get()) }
- single<ChatState> { SharedChatState(get(), get(), get()) } // uiDispatcher, threadBuilder, timeProvider
- factory { LoadSessionUseCase(get(), get<SharedChatState>(), get(), get()) }
- factory { StreamingCoordinator(get(), get<SharedChatState>(), get(), get(), get()) }
- factory { SendMessageUseCase(get(), get<SharedChatState>(), get(), get(), get(), get(), get()) }
- factory { ReplyUseCase(get<SharedChatState>()) }
- factory { EditMessageUseCase(get(), get<SharedChatState>(), get(), get(), get()) }
- factory { DeleteMessageUseCase(get(), get<SharedChatState>(), get()) }
- factory { SwitchBranchUseCase(get(), get(), get<SharedChatState>(), get()) }
- factory { SelectModelUseCase(get(), get<SharedChatState>(), get()) }
- factory { SelectSettingsUseCase(get(), get<SharedChatState>(), get()) }
- factory { UpdateInputUseCase(get<SharedChatState>()) }
- viewModel { ChatViewModel(
    state = get<ChatState>(),
    loadSessionUC = get(),
    sendMessageUC = get(),
    replyUC = get(),
    editMessageUC = get(),
    deleteMessageUC = get(),
    switchBranchUC = get(),
    selectModelUC = get(),
    selectSettingsUC = get(),
    updateInputUC = get(),
    retryCoordinator = get() // optional
  ) }

Note: Adjust to actual module/file structure; keep using kotlinx.datetime Clock via TimeProvider.

---

### Migration Plan (Incremental, Safe)

1) Extract utilities first
- Move buildThreadBranch and findLeafOfBranch into DefaultThreadBuilder.
- Replace internal calls to use threadBuilder. Add unit tests for these functions with various graph shapes (line references 616–651, 653–688).

2) Introduce SharedChatState
- Move all MutableStateFlows and read-only flows into SharedChatState.
- Keep ChatViewModel delegating to SharedChatState for state access.
- Ensure displayedMessages still composes with ThreadBuilder.

3) Move simple UI actions to use-cases
- UpdateInputUseCase, ReplyUseCase, and basic editing state transitions (start/cancel/update content).
- Wire these via DI and delegate from the VM.

4) Move networked actions to use-cases
- LoadSessionUseCase: encapsulate lines 176–227 and the retry metadata updates.
- SelectModel/SelectSettings use-cases.
- SwitchBranchUseCase persists leaf on backend.

5) Handle messaging flows
- SendMessageUseCase including branching on streaming.
- StreamingCoordinator for flow orchestration (lines 281–369) and error handling.

6) Move deleteMessage to DeleteMessageUseCase
- Manage reload through LoadSessionUseCase or direct call; consider backpressure and state consistency.

7) Introduce ErrorNotifier and optional RetryCoordinator
- Replace direct eventBus.emitEvent/apiRequestError calls with ErrorNotifier.
- Provide a RetryCoordinator subscribed to EventBus that consumes the eventId from SharedChatState and triggers retry via LoadSessionUseCase.

8) Thin the ViewModel
- Leave only delegations and state shortcuts.
- Remove business logic from VM.

9) Update tests
- Add unit tests for each use-case in isolation with fake APIs and in-memory SharedChatState.
- Add integration-style tests asserting that state updates propagate to displayedMessages.

10) Clean-up and docs
- Remove obsolete code from VM.
- Document use-cases and their invariants.

---

### Testing Strategy

- ThreadBuilder
  - Upward branch building with gaps, cycles, and missing nodes.
  - Leaf detection with single/multi-child chains and cycle detection.

- SharedChatState
  - Emission correctness for derived displayedMessages when base flows change.
  - Reset behavior (clearSession).

- Use-cases
  - LoadSession: success, error (event emission), retry path.
  - SendMessage (non-streaming): success adds messages, updates leaf, clears reply/input; error emits error.
  - StreamingCoordinator: sequence of events updates temporary and final states; errors clear temp and emit error.
  - Editing: save updates message content; validation for blank text.
  - Delete: triggers reload and ends in consistent state.
  - SwitchBranch: computes leaf, persists, updates state.
  - SelectModel/Settings: update current session fields.

- Concurrency
  - Ensure uiDispatcher is used for state mutations.
  - Use Turbine or similar to assert flows in tests.

---

### Risks and Mitigations

- Risk: State duplication between VM and state holder.
  - Mitigation: Single source of truth in SharedChatState; VM only references read-only flows.

- Risk: Over-fragmentation.
  - Mitigation: Group related actions (e.g., editing-related actions together).

- Risk: Streaming edge-cases.
  - Mitigation: Centralize in StreamingCoordinator with exhaustive event handling and clear state transitions.

- Risk: Retry logic coupling.
  - Mitigation: Encapsulate in LoadSessionUseCase or a dedicated RetryCoordinator.

---

### Open Questions

- Should retry state live entirely in SharedChatState or a dedicated RetryCoordinator?
- What is the canonical source for streaming enabled flag? SettingsApi or local preference?
- Should displayedMessages include streaming temp messages when session state is not Success, or only when successful?
- Are there branch selection policies beyond “first child” traversal in findLeafOfBranch?

---

### Acceptance Criteria

- ChatViewModel contains no direct API calls and minimal logic.
- All state flows are owned by SharedChatState; ViewModel exposes them without duplication.
- Each current public method in ChatViewModel maps to a use-case call.
- Thread building and leaf finding live in ThreadBuilder and are unit-tested.
- Streaming behavior is contained in StreamingCoordinator and unit-tested with simulated events.
- Error emission is unified via ErrorNotifier; retry path works as before.
- All existing UI composables can consume the same public StateFlows with minimal or no change.

---

### Mapping Reference (Old -> New)

- loadSession(...) -> LoadSessionUseCase.execute(...)
- clearSession() -> SharedChatState.clearAll()
- updateInput(text) -> UpdateInputUseCase.execute(text)
- sendMessage() -> SendMessageUseCase.execute()
- startReplyTo(msg), cancelReply() -> ReplyUseCase.start/cancel
- startEditing(msg), updateEditingContent(text), saveEditing(), cancelEditing() -> EditMessageUseCase
- deleteMessage(id) -> DeleteMessageUseCase.execute(id)
- switchBranchToMessage(id) -> SwitchBranchUseCase.execute(id)
- selectModel(modelId) -> SelectModelUseCase.execute(modelId)
- selectSettings(settingsId) -> SelectSettingsUseCase.execute(settingsId)
- buildThreadBranch, findLeafOfBranch -> ThreadBuilder

---

### Notes

- Continue using kotlinx.datetime via TimeProvider; avoid java.time per project guidelines.
- Preserve Arrow Either usage pattern within use-cases; fold results and update state accordingly.
- Keep EventBus usage within ErrorNotifier and any coordinators that need it, not inside the VM.

This plan enables a gradual refactor with minimal disruption to the UI layer while improving modularity, testability, and maintainability.