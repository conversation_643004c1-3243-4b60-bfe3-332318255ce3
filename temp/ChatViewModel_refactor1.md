# ChatViewModel Refactoring Plan

## Current State Analysis

The `ChatViewModel` is currently a large, monolithic class (~750 lines) with multiple responsibilities:

1. **Session State Management** - Loading, clearing, and maintaining session state
2. **Message Threading/Branching** - Building thread branches and managing branch switching
3. **Message Input Management** - Handling user input, reply targets, and editing state
4. **Message Operations** - Sending, editing, deleting messages with both streaming and non-streaming modes
5. **Error Handling & Retry Logic** - Managing API errors and retry mechanisms
6. **Event Bus Integration** - Listening to and emitting events
7. **UI State Coordination** - Coordinating various UI states and flows

## Proposed Modular Architecture

### 1. SessionStateManager
**Responsibilities:**
- Loading and clearing session state
- Managing session-level operations (model/settings selection)
- Handling session-level error states and retry logic

**State:**
- `sessionState: StateFlow<UiState<ApiError, ChatSession>>`
- `lastFailedLoadEventId` and `lastAttemptedSessionId` for retry logic

**Methods:**
- `loadSession(sessionId: Long?, forceReload: Boolean = false)`
- `clearSession()`
- `selectModel(modelId: Long?)`
- `selectSettings(settingsId: Long?)`

### 2. MessageThreadManager
**Responsibilities:**
- Managing thread branching and navigation
- Building displayed message lists from flat message structures
- Handling branch switching logic

**State:**
- `currentBranchLeafId: StateFlow<Long?>`
- `displayedMessages: StateFlow<List<ChatMessage>>`

**Methods:**
- `switchBranchToMessage(targetMessageId: Long)`
- `buildThreadBranch(allMessages: List<ChatMessage>, leafId: Long?): List<ChatMessage>`
- `findLeafOfBranch(startMessageId: Long, messageMap: Map<Long, ChatMessage>): Long?`

### 3. MessageInputManager
**Responsibilities:**
- Managing message input state
- Handling reply targets and editing state
- Input validation

**State:**
- `inputContent: StateFlow<String>`
- `replyTargetMessage: StateFlow<ChatMessage?>`
- `editingMessage: StateFlow<ChatMessage?>`
- `editingContent: StateFlow<String>`

**Methods:**
- `updateInput(newText: String)`
- `startReplyTo(message: ChatMessage)`
- `cancelReply()`
- `startEditing(message: ChatMessage)`
- `updateEditingContent(newText: String)`
- `cancelEditing()`

### 4. MessageOperationsManager
**Responsibilities:**
- Handling message sending (both streaming and non-streaming)
- Message editing and deletion operations
- Managing streaming state

**State:**
- `isSendingMessage: StateFlow<Boolean>`
- `streamingUserMessage: StateFlow<ChatMessage.UserMessage?>`
- `streamingAssistantMessage: StateFlow<ChatMessage.AssistantMessage?>`

**Methods:**
- `sendMessage(content: String, parentId: Long?, sessionId: Long)`
- `saveEditing(messageId: Long, newContent: String)`
- `deleteMessage(messageId: Long)`
- `handleStreamingMessage(...)` (private)
- `handleNonStreamingMessage(...)` (private)

### 5. ChatErrorHandler
**Responsibilities:**
- Centralizing error handling logic
- Managing retry mechanisms
- Event bus error emission

**Methods:**
- `handleApiError(error: ApiError, context: String, isRetryable: Boolean = false)`
- `handleStreamingError(error: ApiError)`
- `emitRetryableError(error: ApiError, shortMessage: String, eventId: String)`

### 6. ChatEventCoordinator
**Responsibilities:**
- Managing event bus integration
- Coordinating between different managers
- Handling cross-cutting concerns

**Methods:**
- `setupEventBusListeners()`
- `handleSnackbarInteraction(event: SnackbarInteractionEvent)`

## Refactored ChatViewModel Structure

```kotlin
class ChatViewModel(
    private val sessionManager: SessionStateManager,
    private val threadManager: MessageThreadManager,
    private val inputManager: MessageInputManager,
    private val operationsManager: MessageOperationsManager,
    private val errorHandler: ChatErrorHandler,
    private val eventCoordinator: ChatEventCoordinator
) : ViewModel() {

    // Expose state from managers
    val sessionState = sessionManager.sessionState
    val currentBranchLeafId = threadManager.currentBranchLeafId
    val displayedMessages = threadManager.displayedMessages
    val inputContent = inputManager.inputContent
    val replyTargetMessage = inputManager.replyTargetMessage
    val editingMessage = inputManager.editingMessage
    val editingContent = inputManager.editingContent
    val isSendingMessage = operationsManager.isSendingMessage

    // Delegate methods to appropriate managers
    fun loadSession(sessionId: Long?, forceReload: Boolean = false) = 
        sessionManager.loadSession(sessionId, forceReload)
    
    fun sendMessage() = operationsManager.sendMessage(
        content = inputManager.inputContent.value,
        parentId = inputManager.replyTargetMessage.value?.id ?: threadManager.currentBranchLeafId.value,
        sessionId = sessionManager.sessionState.value.dataOrNull?.id ?: return
    )
    
    // ... other delegating methods
}
```

## Implementation Strategy

### Phase 1: Extract Core Managers
1. **SessionStateManager** - Extract session loading/clearing logic
2. **MessageThreadManager** - Extract thread building and branch switching
3. **MessageInputManager** - Extract input state management

### Phase 2: Extract Operations & Error Handling
4. **MessageOperationsManager** - Extract message CRUD operations
5. **ChatErrorHandler** - Centralize error handling logic

### Phase 3: Coordination & Integration
6. **ChatEventCoordinator** - Handle event bus and cross-manager coordination
7. **Refactor ChatViewModel** - Transform into coordinator that delegates to managers

### Phase 4: Testing & Validation
- Unit test each manager in isolation
- Integration tests for manager interactions
- Verify UI state flows work correctly

## Benefits of This Approach

### 1. **Single Responsibility Principle**
Each manager has a clear, focused responsibility

### 2. **Improved Testability**
- Each manager can be unit tested in isolation
- Easier to mock dependencies for testing
- Clearer test scenarios for each concern

### 3. **Better Maintainability**
- Changes to one concern don't affect others
- Easier to locate and fix bugs
- Simpler code reviews

### 4. **Enhanced Reusability**
- Managers could be reused in other ViewModels
- Business logic separated from UI concerns

### 5. **Clearer Dependencies**
- Explicit dependencies between managers
- Easier dependency injection setup

## Dependency Flow

```
ChatViewModel
├── SessionStateManager (depends on SessionApi, EventBus)
├── MessageThreadManager (depends on SessionApi)
├── MessageInputManager (no external dependencies)
├── MessageOperationsManager (depends on ChatApi, EventBus)
├── ChatErrorHandler (depends on EventBus)
└── ChatEventCoordinator (depends on EventBus, all managers)
```

## File Structure

```
app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/
├── ChatViewModel.kt (refactored)
├── managers/
│   ├── SessionStateManager.kt
│   ├── MessageThreadManager.kt
│   ├── MessageInputManager.kt
│   ├── MessageOperationsManager.kt
│   ├── ChatErrorHandler.kt
│   └── ChatEventCoordinator.kt
└── contracts/
    ├── ChatStateProvider.kt
    └── MessageOperations.kt
```

## Risk Mitigation

### 1. **State Synchronization**
- Use shared StateFlow instances where managers need to coordinate
- Clear ownership of state mutations
- Event-driven updates between managers

### 2. **Backward Compatibility**
- Maintain same public API in ChatViewModel during transition
- Gradual migration of functionality
- Comprehensive testing at each step

### 3. **Performance**
- Ensure StateFlow combining doesn't create performance bottlenecks
- Monitor memory usage with multiple managers
- Optimize hot paths in message operations

## Next Steps

1. Start with extracting `SessionStateManager` as it has the clearest boundaries
2. Create interfaces for manager contracts to enable testing
3. Set up dependency injection for the new managers
4. Gradually migrate functionality while maintaining tests
5. Update documentation and architectural diagrams

This modular approach will make the codebase more maintainable, testable, and easier to extend with new features.
