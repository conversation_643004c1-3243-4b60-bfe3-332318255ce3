# ChatViewModel Refactoring Plan

## Current Analysis

The current ChatViewModel is a monolithic class (~730 lines) that handles multiple responsibilities:

1. **Session Management**: Loading, clearing, and managing session state
2. **Message Operations**: Sending, editing, deleting messages
3. **Streaming Management**: Handling real-time message streaming
4. **Branch Navigation**: Managing conversation thread branches
5. **Input State**: Managing user input and reply/edit states
6. **Settings Management**: Managing model and settings selection
7. **Error Handling**: Managing retry logic and error states
8. **Thread Building**: Building conversation branches from flat message lists

## Proposed Modular Architecture

### 1. Shared State Container (`ChatState`)

Create a centralized state holder that all use case classes can access:

```kotlin
class ChatState(
    private val uiDispatcher: CoroutineDispatcher,
    private val clock: Clock
) {
    // Session state
    private val _sessionState = MutableStateFlow<UiState<ApiError, ChatSession>>(UiState.Idle)
    val sessionState: StateFlow<UiState<ApiError, ChatSession>> = _sessionState.asStateFlow()
    
    // Branch state
    private val _currentBranchLeafId = MutableStateFlow<Long?>(null)
    val currentBranchLeafId: StateFlow<Long?> = _currentBranchLeafId.asStateFlow()
    
    // Streaming state
    private val _streamingUserMessage = MutableStateFlow<ChatMessage.UserMessage?>(null)
    private val _streamingAssistantMessage = MutableStateFlow<ChatMessage.AssistantMessage?>(null)
    
    // Input states
    private val _inputContent = MutableStateFlow("")
    val inputContent: StateFlow<String> = _inputContent.asStateFlow()
    
    private val _replyTargetMessage = MutableStateFlow<ChatMessage?>(null)
    val replyTargetMessage: StateFlow<ChatMessage?> = _replyTargetMessage.asStateFlow()
    
    // Editing states
    private val _editingMessage = MutableStateFlow<ChatMessage?>(null)
    val editingMessage: StateFlow<ChatMessage?> = _editingMessage.asStateFlow()
    
    private val _editingContent = MutableStateFlow("")
    val editingContent: StateFlow<String> = _editingContent.asStateFlow()
    
    // Operation states
    private val _isSendingMessage = MutableStateFlow(false)
    val isSendingMessage: StateFlow<Boolean> = _isSendingMessage.asStateFlow()
    
    // Error retry states
    private val _lastFailedLoadEventId = MutableStateFlow<String?>(null)
    private val _lastAttemptedSessionId = MutableStateFlow<Long?>(null)
    
    // Computed state: displayed messages
    val displayedMessages: StateFlow<List<ChatMessage>> = combine(
        _sessionState.filterIsInstance<UiState.Success<ChatSession>>().map { it.data.messages },
        _currentBranchLeafId,
        _streamingUserMessage,
        _streamingAssistantMessage
    ) { allPersistedMessages, leafId, streamingUserMessage, streamingAssistantMessage ->
        val messagesForBranching = allPersistedMessages + listOfNotNull(streamingUserMessage, streamingAssistantMessage)
        ThreadBranchBuilder.buildThreadBranch(messagesForBranching, leafId)
    }.stateIn(
        scope = CoroutineScope(uiDispatcher),
        started = SharingStarted.Eagerly,
        initialValue = emptyList()
    )
    
    // State update methods
    fun updateSessionState(state: UiState<ApiError, ChatSession>) { _sessionState.value = state }
    fun updateBranchLeafId(leafId: Long?) { _currentBranchLeafId.value = leafId }
    // ... other update methods
}
```

### 2. Use Case Classes

#### 2.1 Session Management Use Case (`SessionManagementUseCase`)

**Responsibilities:**
- Loading sessions
- Clearing sessions
- Error retry logic
- EventBus integration for session loading

**Methods:**
- `loadSession(sessionId: Long?, forceReload: Boolean = false)`
- `clearSession()`
- `handleSessionLoadRetry(eventId: String)`

#### 2.2 Message Operations Use Case (`MessageOperationsUseCase`)

**Responsibilities:**
- Sending messages (streaming and non-streaming)
- Editing messages
- Deleting messages
- Managing streaming state

**Methods:**
- `sendMessage(content: String, parentId: Long?)`
- `saveEditedMessage(messageId: Long, newContent: String)`
- `deleteMessage(messageId: Long)`
- `handleStreamingUpdate(update: ChatStreamEvent)`

#### 2.3 Input Management Use Case (`InputManagementUseCase`)

**Responsibilities:**
- Managing input text
- Reply target management
- Edit mode management

**Methods:**
- `updateInput(text: String)`
- `startReplyTo(message: ChatMessage)`
- `cancelReply()`
- `startEditing(message: ChatMessage)`
- `updateEditingContent(text: String)`
- `cancelEditing()`

#### 2.4 Branch Navigation Use Case (`BranchNavigationUseCase`)

**Responsibilities:**
- Switching between conversation branches
- Finding leaf messages
- Persisting branch changes

**Methods:**
- `switchBranchToMessage(targetMessageId: Long)`
- `findLeafOfBranch(startMessageId: Long, messageMap: Map<Long, ChatMessage>): Long?`

#### 2.5 Settings Management Use Case (`SettingsManagementUseCase`)

**Responsibilities:**
- Model selection
- Settings profile selection

**Methods:**
- `selectModel(modelId: Long?)`
- `selectSettings(settingsId: Long?)`

### 3. Utility Classes

#### 3.1 Thread Branch Builder (`ThreadBranchBuilder`)

**Responsibilities:**
- Building conversation thread branches
- Message tree traversal logic

**Methods:**
- `buildThreadBranch(allMessages: List<ChatMessage>, leafId: Long?): List<ChatMessage>`

### 4. Refactored ChatViewModel

The new ChatViewModel becomes a facade that:
- Holds references to all use case classes
- Exposes state through the ChatState
- Delegates actions to appropriate use cases
- Manages the ViewModel lifecycle

```kotlin
class ChatViewModel(
    private val sessionApi: SessionApi,
    private val chatApi: ChatApi,
    private val settingsApi: SettingsApi,
    private val eventBus: EventBus,
    private val uiDispatcher: CoroutineDispatcher = Dispatchers.Main,
    private val clock: Clock = Clock.System
) : ViewModel() {

    // Shared state
    private val chatState = ChatState(uiDispatcher, clock)
    
    // Use cases
    private val sessionManagement = SessionManagementUseCase(sessionApi, chatState, eventBus, viewModelScope, uiDispatcher)
    private val messageOperations = MessageOperationsUseCase(chatApi, chatState, eventBus, viewModelScope, uiDispatcher, clock)
    private val inputManagement = InputManagementUseCase(chatState)
    private val branchNavigation = BranchNavigationUseCase(sessionApi, chatState, viewModelScope, uiDispatcher)
    private val settingsManagement = SettingsManagementUseCase(sessionApi, chatState, viewModelScope, uiDispatcher)

    // Expose state
    val sessionState = chatState.sessionState
    val currentBranchLeafId = chatState.currentBranchLeafId
    val displayedMessages = chatState.displayedMessages
    val inputContent = chatState.inputContent
    val replyTargetMessage = chatState.replyTargetMessage
    val editingMessage = chatState.editingMessage
    val editingContent = chatState.editingContent
    val isSendingMessage = chatState.isSendingMessage

    // Delegate actions
    fun loadSession(sessionId: Long?, forceReload: Boolean = false) = 
        sessionManagement.loadSession(sessionId, forceReload)
    
    fun clearSession() = sessionManagement.clearSession()
    
    fun sendMessage() = messageOperations.sendMessage(
        inputContent.value.trim(),
        replyTargetMessage.value?.id ?: currentBranchLeafId.value
    )
    
    fun updateInput(newText: String) = inputManagement.updateInput(newText)
    
    fun startReplyTo(message: ChatMessage) = inputManagement.startReplyTo(message)
    
    fun cancelReply() = inputManagement.cancelReply()
    
    fun startEditing(message: ChatMessage) = inputManagement.startEditing(message)
    
    fun updateEditingContent(newText: String) = inputManagement.updateEditingContent(newText)
    
    fun saveEditing() = messageOperations.saveEditedMessage(
        editingMessage.value?.id ?: return,
        editingContent.value.trim()
    )
    
    fun cancelEditing() = inputManagement.cancelEditing()
    
    fun deleteMessage(messageId: Long) = messageOperations.deleteMessage(messageId)
    
    fun switchBranchToMessage(targetMessageId: Long) = 
        branchNavigation.switchBranchToMessage(targetMessageId)
    
    fun selectModel(modelId: Long?) = settingsManagement.selectModel(modelId)
    
    fun selectSettings(settingsId: Long?) = settingsManagement.selectSettings(settingsId)
}
```

## Implementation Steps

### Phase 1: Create Foundation Classes
1. Create `ChatState` class with all state management
2. Create `ThreadBranchBuilder` utility class
3. Add unit tests for these classes

### Phase 2: Extract Use Cases
1. Create `SessionManagementUseCase`
2. Create `InputManagementUseCase` 
3. Create `BranchNavigationUseCase`
4. Create `MessageOperationsUseCase`
5. Create `SettingsManagementUseCase`
6. Add comprehensive unit tests for each use case

### Phase 3: Refactor ChatViewModel
1. Modify ChatViewModel to use the new architecture
2. Update dependency injection setup
3. Ensure all existing functionality works
4. Run integration tests

### Phase 4: Cleanup and Optimization
1. Remove any unused code
2. Optimize state flow combinations
3. Add documentation
4. Performance testing

## Benefits of This Architecture

1. **Single Responsibility**: Each class has a clear, focused purpose
2. **Testability**: Use cases can be unit tested independently
3. **Reusability**: Use cases could be reused in other ViewModels if needed
4. **Maintainability**: Changes to specific functionality are isolated
5. **Shared State**: Centralized state management prevents inconsistencies
6. **Scalability**: Easy to add new features by creating new use cases

## File Structure

```
app/src/commonMain/kotlin/eu/torvian/chatbot/app/
├── viewmodel/
│   ├── ChatViewModel.kt (refactored)
│   ├── state/
│   │   └── ChatState.kt
│   ├── usecase/
│   │   ├── SessionManagementUseCase.kt
│   │   ├── MessageOperationsUseCase.kt
│   │   ├── InputManagementUseCase.kt
│   │   ├── BranchNavigationUseCase.kt
│   │   └── SettingsManagementUseCase.kt
│   └── utils/
│       └── ThreadBranchBuilder.kt
```

## Dependencies and Injection

Each use case will receive only the dependencies it needs:
- APIs (sessionApi, chatApi, etc.)
- ChatState reference
- ViewModelScope (where needed)
- EventBus (where needed)
- Dispatchers and Clock (where needed)

This ensures clean dependency graphs and easier testing.

## Testing Strategy

1. **Unit Tests**: Each use case tested in isolation with mocked dependencies
2. **Integration Tests**: ChatViewModel tested with real use cases but mocked APIs
3. **State Tests**: ChatState tested for proper state transitions
4. **Utility Tests**: ThreadBranchBuilder tested with various message structures

This modular architecture will make the ChatViewModel much more maintainable while preserving all existing functionality.
