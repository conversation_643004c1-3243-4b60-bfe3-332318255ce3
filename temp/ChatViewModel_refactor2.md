# ChatViewModel Modularization Plan

Status: Plan only (no code changes yet)
Scope: app/src/commonMain/.../viewmodel/ChatViewModel.kt and closely-related app services

1) Context and current responsibilities
- Source reviewed: app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/ChatViewModel.kt
- Related docs reviewed: docs/Project and Package Structure.md
- Current ChatViewModel responsibilities (high-level):
  - Session lifecycle: load, clear, persist leaf switch, select model/settings
  - Message composition state: input text, reply target, editing state/content, sending flag
  - Threading logic: buildThreadBranch, findLeafOfBranch (pure utilities embedded in VM)
  - Message operations: send (streaming and non-streaming), edit, delete
  - Streaming orchestration: collect streaming updates, incrementally update UI state
  - Global error handling and retry via EventBus and SnackbarInteractionEvent
  - Derived UI state: displayedMessages combining persisted + streaming messages and branch leaf

Pain points
- Class is large and mixes orchestration, UI state storage, business workflows, and pure utilities.
- Streaming and non-streaming paths interleaved with state updates, which hinders testability.
- Retry/event handling logic intertwined with session loading.

Goals and constraints
- Keep the ViewModel public API to the UI stable for now (StateFlows and public functions unchanged).
- Extract cohesive, testable components with clear contracts.
- Keep dependencies aligned with current modules and Koin DI style.
- Prefer pure functions for threading operations, easy unit tests.
- Ensure Arrow Either and kotlinx.datetime remain the standard throughout.

2) Target modular decomposition
A) Thread utilities (pure, stateless)
- ThreadNavigator
  - Responsibility: findLeafOfBranch(startId, messageMap) -> Long?
  - Notes: carry over cycle detection and warnings; no side effects.
- ThreadBranchBuilder
  - Responsibility: buildThreadBranch(allMessages, leafId) -> List<ChatMessage>
  - Notes: pure order-building with cycle/data-integrity checks.
- Package: eu.torvian.chatbot.app.viewmodel.thread
- Benefits: independent tests, reusable across VM/tests.

B) State store for session UI state
- ChatSessionStateStore
  - Holds and exposes StateFlows currently in ChatViewModel:
    - sessionState, currentBranchLeafId, inputContent, replyTargetMessage,
      editingMessage, editingContent, isSendingMessage,
      streamingUserMessage, streamingAssistantMessage
  - Provides small, focused mutator methods (setters/clearers) used by orchestration layers.
  - Does not perform network calls.
- Package: eu.torvian.chatbot.app.viewmodel.state
- Benefits: centralizes state, simplifies VM, easier to test state transitions.

C) Message sending orchestrator (workflow)
- MessageSendOrchestrator
  - Responsibility: handle sending via ChatApi in both streaming and non-streaming modes.
  - API (proposal):
    - fun send(
        session: ChatSession,
        content: String,
        parentId: Long?,
        streamingEnabled: Boolean
      ): Flow<SendEvent>
  - SendEvent (sealed):
    - UserMessageSaved(message: ChatMessage.UserMessage)
    - AssistantStart(message: ChatMessage.AssistantMessage)
    - AssistantDelta(deltaContent: String)
    - AssistantEnd(finalUser: ChatMessage.UserMessage, finalAssistant: ChatMessage.AssistantMessage)
    - NonStreamingComplete(newMessages: List<ChatMessage>)
    - ErrorOccurred(error: ApiError)
    - Completed
  - Orchestrator emits events only; the VM/state store applies mutations.
  - Package: eu.torvian.chatbot.app.viewmodel.send
- Benefits: isolates streaming complexity, enables narrow unit tests, easier error injection.

D) Message editing and deletion service (workflow)
- MessageEditor
  - edit(messageId, newContent): Either<ApiError, ChatMessage>
  - delete(messageId): Either<ApiError, Unit>
- Package: eu.torvian.chatbot.app.viewmodel.edit
- Benefits: concentrates message mutation paths, separable tests.

E) Session coordination (workflow)
- SessionCoordinator
  - loadSession(sessionId): Either<ApiError, ChatSession>
  - updateLeaf(sessionId, leafId): Either<ApiError, Unit>
  - selectModel(sessionId, modelId): Either<ApiError, Unit>
  - selectSettings(sessionId, settingsId): Either<ApiError, Unit>
- Package: eu.torvian.chatbot.app.viewmodel.session
- Benefits: clean boundary for all SessionApi operations used by the VM.

F) Retry coordinator
- LoadRetryCoordinator
  - Tracks lastFailedLoadEventId and lastAttemptedSessionId
  - Provides helpers to produce snackbar events and decide on retries upon SnackbarInteractionEvent
- Package: eu.torvian.chatbot.app.viewmodel.retry
- Benefits: untangles retry bookkeeping from main logic.

G) Feature flags / preferences abstraction
- ChatFeatureFlags
  - isStreamingEnabled(): Boolean (later can consult SettingsApi or local prefs)
- Package: eu.torvian.chatbot.app.viewmodel.prefs
- Benefits: removes TODO from VM and centralizes behavior switches.

H) Error event builder
- ApiErrorEventFactory
  - apiRequestError(apiError, shortMessage, isRetryable=false): Global error event
  - Package: eu.torvian.chatbot.app.viewmodel.error
  - Alternatively, keep using existing function from domain.events if that’s already a stable facade; only centralize formatting choices here if needed.

3) Resulting ChatViewModel responsibilities (after refactor)
- Dependency holder and orchestrator wiring:
  - Inject: SessionCoordinator, MessageSendOrchestrator, MessageEditor, ChatSessionStateStore,
    ThreadNavigator, ThreadBranchBuilder, LoadRetryCoordinator, ChatFeatureFlags, EventBus, Clock
- Provide the same StateFlows to UI by exposing those from the store and a combined displayedMessages flow:
  - displayedMessages built by combining sessionState, currentLeafId, streaming states, then using ThreadBranchBuilder
- Provide the same public functions delegating to services:
  - loadSession, clearSession, sendMessage, startReplyTo, cancelReply, startEditing,
    updateEditingContent, saveEditing, cancelEditing, deleteMessage,
    switchBranchToMessage, selectModel, selectSettings, updateInput
- Contain only thin glue logic to translate service events into state mutations and global events.

4) DI and packaging
- Maintain app module ownership; implementations live under app/src/commonMain/...
- Koin wiring (in appModule.kt):
  - single { ThreadNavigator() }
  - single { ThreadBranchBuilder() }
  - single { ChatSessionStateStore(clock = get(), uiDispatcher = get()) }
  - single { MessageSendOrchestrator(chatApi = get(), clock = get()) }
  - single { MessageEditor(chatApi = get(), clock = get()) }
  - single { SessionCoordinator(sessionApi = get(), clock = get()) }
  - single { LoadRetryCoordinator(eventBus = get()) }
  - single<ChatFeatureFlags> { DefaultChatFeatureFlags(settingsApi = get()) }
  - factory { ChatViewModel(/* inject above */) }
- Note: initial version can inline Clock/UI dispatcher in VM and pass to state store to avoid over-injection.

5) Testing strategy
- Pure utils
  - ThreadNavigator: direct tests for linear chains, branches with first-child path, cycles, broken links.
  - ThreadBranchBuilder: tests for ordered root-to-leaf construction, missing nodes, cycles.
- Orchestrators/services
  - MessageSendOrchestrator: use a fake ChatApi to simulate
    - streaming sequence: UserSaved -> AssistantStart -> N deltas -> AssistantEnd -> Completed
    - out-of-order or missing events -> ErrorOccurred propagation
    - non-streaming path -> NonStreamingComplete with correct aggregation
  - SessionCoordinator: stub SessionApi; verify error/success mapping.
  - MessageEditor: stub ChatApi; verify updated message mapping.
  - LoadRetryCoordinator: simulate SnackbarInteractionEvent action/dismiss flows.
- ChatViewModel integration
  - Verify public flows respond correctly for: load success/error with retry, send streaming/non-streaming, edit/save, delete and reload, branch switch and leaf persist, select model/settings.
- Keep existing tests green; add new ones targeted to extracted classes.

6) Migration plan (incremental, small PRs)
- PR1: Extract pure utils
  - Move findLeafOfBranch -> ThreadNavigator
  - Move buildThreadBranch -> ThreadBranchBuilder
  - Replace internal calls with injected utils inside VM
  - Add unit tests for both
- PR2: Introduce ChatSessionStateStore
  - Move all MutableStateFlow fields to the store
  - VM delegates reads/writes; public API unchanged
  - Add minimal tests validating state transitions (store-level)
- PR3: MessageSendOrchestrator
  - Extract handleStreamingMessage and handleNonStreamingMessage logic
  - VM subscribes to SendEvent flow and mutates the state store accordingly
  - Add orchestrator tests with fake ChatApi
- PR4: SessionCoordinator
  - Extract loadSession, updateLeaf, selectModel, selectSettings
  - VM handles only optimistic updates and event emissions; IO in coordinator
  - Add coordinator tests
- PR5: MessageEditor
  - Extract saveEditing and deleteMessage calls
  - VM focuses on mapping results to state; add editor tests
- PR6: LoadRetryCoordinator
  - Extract snackbar retry bookkeeping and event handling
  - VM subscribes to coordinator outputs; add tests
- PR7: FeatureFlags
  - Replace TODO with ChatFeatureFlags; default to true for streaming until settings are wired
  - Add a small test for flag check path selection
- PR8: Cleanups and docs
  - Update Koin module bindings
  - Update docs and ensure all tests pass

7) Edge cases and correctness considerations
- Streaming
  - Ensure deltas apply only after AssistantStart; ignore stray deltas.
  - Cancel on ErrorOccurred: clear streaming state consistently (user and assistant temp entries), emit error.
  - Completed after AssistantEnd or early completion: idempotent handling.
- Session load and retry
  - Clear retry IDs on success, avoid duplicate retries.
  - Avoid reloading when already loading or target session is the same (existing optimization preserved).
- Threading
  - Handle cycles and missing nodes gracefully; return empty branch to avoid partial/corrupt UI.
- Editing
  - Trim content; reject blank saves at VM/UI boundary; keep behavior stable.
- Deletion
  - After delete success, reload session; ensure isSendingMessage flag unaffected.
- Concurrency
  - Avoid race between streaming updates and final session update; favor final session copy as the source of truth.

8) Acceptance criteria
- Public VM API unchanged for UI components.
- All prior features behave the same or better:
  - Loading with retry, branch navigation, sending (streaming/non-streaming), edit, delete, select model/settings.
- New units tests for extracted components; existing tests still pass.
- Improved readability: ChatViewModel under ~200–250 lines of glue logic.

9) File and package plan (proposed)
- eu/torvian/chatbot/app/viewmodel/thread/
  - ThreadNavigator.kt
  - ThreadBranchBuilder.kt
- eu/torvian/chatbot/app/viewmodel/state/
  - ChatSessionStateStore.kt
- eu/torvian/chatbot/app/viewmodel/send/
  - MessageSendOrchestrator.kt (+ SendEvent sealed type)
- eu/torvian/chatbot/app/viewmodel/edit/
  - MessageEditor.kt
- eu/torvian/chatbot/app/viewmodel/session/
  - SessionCoordinator.kt
- eu/torvian/chatbot/app/viewmodel/retry/
  - LoadRetryCoordinator.kt
- eu/torvian/chatbot/app/viewmodel/prefs/
  - ChatFeatureFlags.kt
  - DefaultChatFeatureFlags.kt
- Koin wiring updates in app/src/commonMain/.../koin/appModule.kt

10) Risk and mitigation
- Risk: Behavior drift during delegation
  - Mitigation: incremental PRs, per-step unit tests, maintain API surface, keep logic identical initially.
- Risk: Over-abstraction
  - Mitigation: start with minimal interfaces; keep components thin and purpose-built.
- Risk: DI complexity
  - Mitigation: prefer singletons for stateless utils; factories only where necessary.

11) Rollback plan
- Each PR is self-contained; revert the last PR to roll back a step without impacting earlier stable extractions.

12) Timeline (indicative)
- PR1–PR2: 0.5–1 day
- PR3: 0.5–1 day
- PR4–PR5: 0.5–1 day
- PR6–PR7: 0.5 day
- PR8: 0.25 day

Notes
- Keep using kotlinx.datetime and Arrow Either as per project guidelines.
- This plan aligns with the documented app module structure and keeps code within the app module.

