```kotlin
// Original file:
//app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/ChatViewModel.kt
package eu.torvian.chatbot.app.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import eu.torvian.chatbot.app.domain.contracts.UiState
import eu.torvian.chatbot.app.domain.events.SnackbarInteractionEvent
import eu.torvian.chatbot.app.domain.events.apiRequestError
import eu.torvian.chatbot.app.generated.resources.Res
import eu.torvian.chatbot.app.generated.resources.error_loading_session
import eu.torvian.chatbot.app.generated.resources.error_sending_message_short
import eu.torvian.chatbot.app.service.api.ChatApi
import eu.torvian.chatbot.app.service.api.SessionApi
import eu.torvian.chatbot.app.service.api.SettingsApi
import eu.torvian.chatbot.app.service.misc.EventBus
import eu.torvian.chatbot.app.utils.misc.kmpLogger
import eu.torvian.chatbot.common.api.ApiError
import eu.torvian.chatbot.common.models.*
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import kotlinx.datetime.Clock
import org.jetbrains.compose.resources.getString

/**
 * Manages the UI state for the main chat area of the currently active session,
 * using KMP ViewModel, StateFlow, Arrow's Either, and UiState for loading states.
 *
 * This class is responsible for:
 * - Holding the state (Idle/Loading/Success/Error) of the current session and its messages.
 * - Structuring the flat list of messages into a threaded view for display using Flows.
 * - Managing the state of the message input area.
 * - Handling user actions like sending messages, replying, editing, and deleting messages.
 * - Communicating with the backend via the ChatApi and SessionApi, handling their Either results.
 * - Managing the currently displayed thread branch based on the session's leaf message state.
 *
 * @constructor
 * @param sessionApi The API client for session-related operations.
 * @param chatApi The API client for chat message-related operations.
 * @param settingsApi The API client for model settings.
 * @param eventBus The event bus for emitting global events like retry-able errors.
 * @param uiDispatcher The dispatcher to use for UI-related coroutines. Defaults to Main.
 * @param clock The clock to use for timestamping. Defaults to System clock.
 *
 * @property sessionState The state of the currently loaded chat session, including all messages.
 * @property currentBranchLeafId The ID of the leaf message in the currently displayed thread branch.
 * @property displayedMessages The list of messages to display in the UI, representing the currently selected thread branch.
 * @property inputContent The current text content in the message input field.
 * @property replyTargetMessage The message the user is currently explicitly replying to via the Reply action.
 * @property editingMessage The message currently being edited (E3.S1, E3.S2).
 * @property editingContent The content of the message currently being edited (E3.S1, E3.S2).
 */
class ChatViewModel(
    private val sessionApi: SessionApi,
    private val chatApi: ChatApi,
    private val settingsApi: SettingsApi,
    private val eventBus: EventBus,
    private val uiDispatcher: CoroutineDispatcher = Dispatchers.Main,
    private val clock: Clock = Clock.System
) : ViewModel() {

    private val logger = kmpLogger<ChatViewModel>()

    // --- Observable State for Compose UI (using StateFlow) ---

    private val _sessionState = MutableStateFlow<UiState<ApiError, ChatSession>>(UiState.Idle)

    /**
     * The state of the currently loaded chat session, including all messages.
     * When in Success state, provides the ChatSession object.
     */
    val sessionState: StateFlow<UiState<ApiError, ChatSession>> = _sessionState.asStateFlow()

    private val _currentBranchLeafId = MutableStateFlow<Long?>(null)

    /**
     * The ID of the leaf message in the currently displayed thread branch.
     * Changing this triggers the UI to show a different branch.
     * Null if the session is empty or not loaded/successful.
     */
    val currentBranchLeafId: StateFlow<Long?> = _currentBranchLeafId.asStateFlow()

    // New state to hold the actively streaming assistant message
    private val _streamingAssistantMessage = MutableStateFlow<ChatMessage.AssistantMessage?>(null)

    // New state to hold the temporary user message during streaming
    private val _streamingUserMessage = MutableStateFlow<ChatMessage.UserMessage?>(null)

    /**
     * The list of messages to display in the UI, representing the currently selected thread branch.
     * This is derived from the session's full list of messages and the current leaf message ID,
     * combined with any actively streaming message.
     */
    val displayedMessages: StateFlow<List<ChatMessage>> = combine(
        _sessionState.filterIsInstance<UiState.Success<ChatSession>>()
            .map { it.data.messages },
        _currentBranchLeafId,
        _streamingUserMessage,
        _streamingAssistantMessage
    ) { allPersistedMessages, leafId, streamingUserMessage, streamingAssistantMessage ->
        val messagesForBranching = allPersistedMessages + listOfNotNull(streamingUserMessage, streamingAssistantMessage)
        buildThreadBranch(messagesForBranching, leafId)
    }
        .stateIn(
            scope = CoroutineScope(viewModelScope.coroutineContext + uiDispatcher),
            started = SharingStarted.Eagerly,
            initialValue = emptyList()
        )

    private val _inputContent = MutableStateFlow("")

    /**
     * The current text content in the message input field.
     */
    val inputContent: StateFlow<String> = _inputContent.asStateFlow()

    private val _replyTargetMessage = MutableStateFlow<ChatMessage?>(null)

    /**
     * The message the user is currently explicitly replying to via the Reply action (E1.S7).
     * If null, sending a message replies to the [currentBranchLeafId] value.
     */
    val replyTargetMessage: StateFlow<ChatMessage?> = _replyTargetMessage.asStateFlow()

    private val _editingMessage = MutableStateFlow<ChatMessage?>(null)

    /**
     * The message currently being edited (E3.S1, E3.S2). Null if no message is being edited.
     */
    val editingMessage: StateFlow<ChatMessage?> = _editingMessage.asStateFlow()

    private val _editingContent = MutableStateFlow("")

    /**
     * The content of the message currently being edited (E3.S1, E3.S2).
     */
    val editingContent: StateFlow<String> = _editingContent.asStateFlow()

    private val _isSendingMessage = MutableStateFlow(false)

    /**
     * Indicates whether a message is currently in the process of being sent. (E1.S3)
     */
    val isSendingMessage: StateFlow<Boolean> = _isSendingMessage.asStateFlow()

    // Store the ID of the last emitted error if a retry is possible
    private val _lastFailedLoadEventId = MutableStateFlow<String?>(null)

    // Store the session ID for retry functionality
    private val _lastAttemptedSessionId = MutableStateFlow<Long?>(null)

    init {
        // ViewModel can listen to the EventBus for its own emitted event's responses
        viewModelScope.launch(uiDispatcher) {
            eventBus.events.collect { event ->
                if (event is SnackbarInteractionEvent && event.originalAppEventId == _lastFailedLoadEventId.value) {
                    if (event.isActionPerformed) {
                        logger.info("Retrying loadSession due to Snackbar action!")
                        _lastFailedLoadEventId.value = null // Clear ID before retrying
                        _lastAttemptedSessionId.value?.let { sessionId ->
                            loadSession(sessionId, forceReload = true) // Trigger retry
                        }
                    } else { // It was dismissed (by user or timeout)
                        logger.info("Snackbar dismissed, not retrying loadSession.")
                        _lastFailedLoadEventId.value = null
                        _lastAttemptedSessionId.value = null
                    }
                }
            }
        }
    }

    // --- Public Action Functions (Called by UI Components) ---

    /**
     * Loads a chat session and its messages by ID.
     * Triggered when a session is selected in the session list (E2.S4).
     *
     * @param sessionId The ID of the session to load, or null to clear the session.
     * @param forceReload If true, reloads the session even if it's already loaded successfully.
     */
    fun loadSession(sessionId: Long?, forceReload: Boolean = false) {
        // Prevent reloading if already loading or if the session is already loaded successfully
        val currentState = _sessionState.value
        if (!forceReload && (currentState.isLoading || (currentState.dataOrNull?.id == sessionId))) return

        if (sessionId == null) {
            clearSession()
            return
        }

        // Store the session ID for potential retry
        _lastAttemptedSessionId.value = sessionId

        viewModelScope.launch(uiDispatcher) {
            _sessionState.value = UiState.Loading
            _replyTargetMessage.value = null
            _editingMessage.value = null
            _currentBranchLeafId.value = null

            sessionApi.getSessionDetails(sessionId)
                .fold(
                    ifLeft = { error ->
                        // Handle Error case (E1.S6)
                        _sessionState.value = UiState.Error(error)
                        // Emit to generic EventBus using the specific error type
                        val globalError = apiRequestError(
                            apiError = error,
                            shortMessage = getString(Res.string.error_loading_session),
                            isRetryable = true
                        )
                        _lastFailedLoadEventId.value = globalError.eventId // Store its ID
                        eventBus.emitEvent(globalError)
                    },
                    ifRight = { session ->
                        // Handle Success case (E2.S4)
                        _sessionState.value = UiState.Success(session) // Success payload is the ChatSession
                        // Set initial leaf to the session's saved leaf ID or null if no messages
                        _currentBranchLeafId.value = session.currentLeafMessageId
                        // Clear retry state on success
                        _lastFailedLoadEventId.value = null
                        _lastAttemptedSessionId.value = null
                    }
                )
        }
    }

    /**
     * Clears the currently loaded session state.
     * Called when the selected session is deleted or potentially on app exit.
     */
    fun clearSession() {
        _sessionState.value = UiState.Idle // Go back to idle/no session state
        _replyTargetMessage.value = null
        _editingMessage.value = null
        _currentBranchLeafId.value = null
        _streamingAssistantMessage.value = null
        _streamingUserMessage.value = null
    }

    /**
     * Updates the content of the message input field.
     *
     * @param newText The new text from the input field.
     */
    fun updateInput(newText: String) {
        _inputContent.value = newText
    }

    /**
     * Sends the current message content to the active session.
     * Determines the parent based on [replyTargetMessage] or [currentBranchLeafId].
     * (E1.S1, E1.S7)
     */
    fun sendMessage() {
        val currentSession = _sessionState.value.dataOrNull ?: return // Cannot send if no session loaded successfully
        val content = _inputContent.value.trim()
        if (content.isBlank()) return // Cannot send empty message

        val parentId = _replyTargetMessage.value?.id ?: _currentBranchLeafId.value // Use value from StateFlow

        viewModelScope.launch(uiDispatcher) {
            _isSendingMessage.value = true // Set sending state to true (E1.S3)

            try {
                // Check if streaming is enabled in settings
                val isStreamingEnabled = true // TODO: Implement settings check

                if (isStreamingEnabled) {
                    handleStreamingMessage(currentSession, content, parentId)
                } else {
                    handleNonStreamingMessage(currentSession, content, parentId)
                }
            } finally {
                _isSendingMessage.value = false // Always reset sending state
            }
        }
    }

    /**
     * Handles streaming message processing.
     */
    private suspend fun handleStreamingMessage(currentSession: ChatSession, content: String, parentId: Long?) {
        // Clear any previous streaming state
        _streamingUserMessage.value = null
        _streamingAssistantMessage.value = null

        chatApi.processNewMessageStreaming(
            sessionId = currentSession.id,
            request = ProcessNewMessageRequest(content = content, parentMessageId = parentId)
        ).collect { eitherUpdate ->
            eitherUpdate.fold(
                ifLeft = { error ->
                    logger.error("Streaming message API error: ${error.code} - ${error.message}")
                    // Clear any streaming state and emit error
                    _streamingAssistantMessage.value = null
                    _streamingUserMessage.value = null
                    eventBus.emitEvent(
                        apiRequestError(
                            apiError = error,
                            shortMessage = getString(Res.string.error_sending_message_short),
                        )
                    )
                },
                ifRight = { chatUpdate ->
                    when (chatUpdate) {
                        is ChatStreamEvent.UserMessageSaved -> {
                            // Store the user message in the temporary streaming state
                            _streamingUserMessage.value = chatUpdate.message
                            _currentBranchLeafId.value = chatUpdate.message.id
                            // Clear input and reply target after user message is confirmed
                            _inputContent.value = ""
                            _replyTargetMessage.value = null
                        }

                        is ChatStreamEvent.AssistantMessageStart -> {// Use the assistant message directly from the update
                            _currentBranchLeafId.value = chatUpdate.assistantMessage.id
                            _streamingAssistantMessage.value = chatUpdate.assistantMessage
                        }

                        is ChatStreamEvent.AssistantMessageDelta -> {// Update the streaming message content
                            _streamingAssistantMessage.value?.let { currentStreamingMessage ->
                                _streamingAssistantMessage.value = currentStreamingMessage.copy(
                                    content = currentStreamingMessage.content + chatUpdate.deltaContent
                                )
                            }
                        }

                        is ChatStreamEvent.AssistantMessageEnd -> {
                            // Update the session with the new messages
                            val updatedMessages =
                                currentSession.messages + chatUpdate.finalUserMessage + chatUpdate.finalAssistantMessage
                            val newLeafId = chatUpdate.finalAssistantMessage.id

                            _sessionState.value = UiState.Success(
                                currentSession.copy(
                                    messages = updatedMessages,
                                    currentLeafMessageId = newLeafId,
                                    updatedAt = clock.now()
                                )
                            )
                            _currentBranchLeafId.value = newLeafId
                            _streamingAssistantMessage.value = null // Clear streaming state
                            _streamingUserMessage.value = null // Clear streaming user message
                        }

                        is ChatStreamEvent.ErrorOccurred -> {
                            // Handle error during streaming
                            logger.error("Streaming error: ${chatUpdate.error.message}")
                            _streamingAssistantMessage.value = null
                            _streamingUserMessage.value = null
                            eventBus.emitEvent(
                                apiRequestError(
                                    apiError = chatUpdate.error,
                                    shortMessage = getString(Res.string.error_sending_message_short),
                                )
                            )
                        }

                        ChatStreamEvent.StreamCompleted -> {
                            // Streaming completed successfully
                            logger.info("Streaming completed for session ${currentSession.id}")
                        }
                    }
                }
            )
        }
    }

    /**
     * Handles non-streaming message processing.
     */
    private suspend fun handleNonStreamingMessage(currentSession: ChatSession, content: String, parentId: Long?) {
        chatApi.processNewMessage(
            sessionId = currentSession.id,
            request = ProcessNewMessageRequest(content = content, parentMessageId = parentId)
        ).fold(
            ifLeft = { error ->
                logger.error("Send message API error: ${error.code} - ${error.message}")
                eventBus.emitEvent(
                    apiRequestError(
                        apiError = error,
                        shortMessage = getString(Res.string.error_sending_message_short),
                    )
                )
            },
            ifRight = { newMessages ->
                // Add the new messages to the current session's messages
                val updatedMessages = currentSession.messages + newMessages
                val newLeafId = newMessages.lastOrNull()?.id

                // Update the session object inside the Success state with the new messages
                _sessionState.value = UiState.Success(
                    currentSession.copy(
                        messages = updatedMessages,
                        currentLeafMessageId = newLeafId,
                        updatedAt = clock.now()
                    )
                )
                _currentBranchLeafId.value = newLeafId
                _replyTargetMessage.value = null
                _inputContent.value = ""
            }
        )
    }

    /**
     * Sets the state to indicate the user is replying to a specific message (E1.S7).
     *
     * @param message The message to reply to.
     */
    fun startReplyTo(message: ChatMessage) {
        _replyTargetMessage.value = message
        // Optionally, trigger scroll action in UI
    }

    /**
     * Cancels the specific reply target, reverting to replying to the current leaf (E1.S7).
     */
    fun cancelReply() {
        _replyTargetMessage.value = null
    }

    /**
     * Sets the state to indicate a message is being edited (E3.S1, E3.S2).
     *
     * @param message The message to edit.
     */
    fun startEditing(message: ChatMessage) {
        _editingMessage.value = message
        _editingContent.value = message.content
    }

    /**
     * Updates the content of the message currently being edited (E3.S1, E3.S2).
     * Called by the UI as the user types in the editing input field.
     *
     * @param newText The new text content for the editing field.
     */
    fun updateEditingContent(newText: String) {
        _editingContent.value = newText
    }

    /**
     * Saves the edited message content (E3.S3).
     */
    fun saveEditing() {
        val messageToEdit = _editingMessage.value ?: return
        val newContent = _editingContent.value.trim()
        if (newContent.isBlank()) {
            // Show inline validation error (UI concern) or update state
            println("Validation Error: Message content cannot be empty.")
            return
        }
        val currentSession = _sessionState.value.dataOrNull ?: return

        viewModelScope.launch(uiDispatcher) {
            // Optionally show inline loading/saving state for the specific message being edited
            chatApi.updateMessageContent(messageToEdit.id, UpdateMessageRequest(newContent))
                .fold(
                    ifLeft = { error ->
                        println("Edit message API error: ${error.code} - ${error.message}")
                        // Show inline error for the edited message (UI concern)
                    },
                    ifRight = { updatedMessage ->
                        // Update the message in the messages list within the current session state Flow (E3.S3)
                        val updatedAllMessages = currentSession.messages.map {
                            if (it.id == updatedMessage.id) updatedMessage else it
                        }
                        _sessionState.value = UiState.Success(
                            currentSession.copy(
                                messages = updatedAllMessages,
                                updatedAt = clock.now()
                            )
                        )
                        // displayedMessages StateFlow derived state will react

                        // Clear editing state on success
                        _editingMessage.value = null
                        _editingContent.value = ""
                    }
                )
        }
    }

    /**
     * Cancels the message editing state (E3.S1, E3.S2).
     */
    fun cancelEditing() {
        _editingMessage.value = null
        _editingContent.value = ""
    }

    /**
     * Deletes a specific message and its children from the session (E3.S4).
     *
     * @param messageId The ID of the message to delete.
     */
    fun deleteMessage(messageId: Long) {
        val currentSession = _sessionState.value.dataOrNull ?: return
        viewModelScope.launch(uiDispatcher) {
            // Optionally show inline loading state for the specific message being deleted
            chatApi.deleteMessage(messageId)
                .fold(
                    ifLeft = { error ->
                        println("Delete message API error: ${error.code} - ${error.message}")
                        // Show transient error message
                    },
                    ifRight = {
                        // Backend handled deletion recursively (E3.S4).
                        // Reload the session to update the UI state correctly (V1.1 strategy).
                        // This action launches a new coroutine within viewModelScope.
                        loadSession(currentSession.id, forceReload = true)
                    }
                )
        }
    }

    /**
     * Switches the currently displayed chat branch to the one that includes the given message ID.
     * The ViewModel will find the actual leaf message of this branch by traversing down
     * the path of first children starting from the provided `targetMessageId`.
     * This new leaf message ID is then persisted to the session record (E1.S5).
     *
     * @param targetMessageId The ID of the message that serves as the starting point for
     *                        determining the new displayed branch. This message itself may be
     *                        a root, middle, or leaf message in the conversation tree.
     */
    fun switchBranchToMessage(targetMessageId: Long) {
        val currentSession = _sessionState.value.dataOrNull ?: return
        if (_currentBranchLeafId.value == targetMessageId) return

        val messageMap = currentSession.messages.associateBy { it.id }

        // Use the new helper function to find the actual leaf ID
        val finalLeafId = findLeafOfBranch(targetMessageId, messageMap)
        if (finalLeafId == null) {
            println("Warning: Could not determine a valid leaf for branch starting with $targetMessageId.")
            return
        }

        if (_currentBranchLeafId.value == finalLeafId) return // Already on this exact branch

        viewModelScope.launch(uiDispatcher) {
            // Optimistically update UI state Flow first for responsiveness
            _currentBranchLeafId.value = finalLeafId

            // Persist the change to the backend (E1.S5 requirement)
            sessionApi.updateSessionLeafMessage(currentSession.id, UpdateSessionLeafMessageRequest(finalLeafId))
                .fold(
                    ifLeft = { error ->
                        println("Update leaf message API error: ${error.code} - ${error.message}")
                        // Decide rollback strategy if needed, or just show a transient error message.
                    },
                    ifRight = {
                        // Persistence successful.
                        // We should also update the leafMessageId in the session object itself
                        // within the state Flow to keep the ChatSession data consistent.
                        _sessionState.value = UiState.Success(
                            currentSession.copy(currentLeafMessageId = finalLeafId)
                        )
                    }
                )
        }
    }

    /**
     * Sets the selected model for the current session (E4.S7).
     *
     * @param modelId The ID of the model to select, or null to unset.
     */
    fun selectModel(modelId: Long?) {
        val currentSession = _sessionState.value.dataOrNull ?: return
        viewModelScope.launch(uiDispatcher) {
            sessionApi.updateSessionModel(currentSession.id, UpdateSessionModelRequest(modelId))
                .fold(
                    ifLeft = { error ->
                        println("Update session model API error: ${error.code} - ${error.message}")
                        // Show error
                    },
                    ifRight = {
                        // Update the session object within the state Flow manually as backend doesn't return it
                        _sessionState.value = UiState.Success(
                            currentSession.copy(currentModelId = modelId)
                        )
                    }
                )
        }
    }

    /**
     * Sets the selected settings profile for the current session (E4.S7).
     *
     * @param settingsId The ID of the settings profile to select, or null to unset.
     */
    fun selectSettings(settingsId: Long?) {
        val currentSession = _sessionState.value.dataOrNull ?: return
        viewModelScope.launch(uiDispatcher) {
            sessionApi.updateSessionSettings(currentSession.id, UpdateSessionSettingsRequest(settingsId))
                .fold(
                    ifLeft = { error ->
                        println("Update session settings API error: ${error.code} - ${error.message}")
                        // Show error
                    },
                    ifRight = {
                        // Update the session object within the state Flow manually
                        _sessionState.value = UiState.Success(
                            currentSession.copy(currentSettingsId = settingsId)
                        )
                    }
                )
        }
    }

    /**
     * Finds the ultimate leaf message ID by traversing down the
     * first child path from a given starting message ID.
     *
     * @param startMessageId The ID of the message to start the traversal from.
     * @param messageMap A map of all messages in the session for efficient lookup.
     * @return The ID of the leaf message found, or null if the startMessageId is invalid or a cycle/broken link is detected.
     */
    private fun findLeafOfBranch(startMessageId: Long, messageMap: Map<Long, ChatMessage>): Long? {
        var currentPathMessage: ChatMessage? = messageMap[startMessageId]
        if (currentPathMessage == null) {
            println("Warning: Starting message for branch traversal not found: $startMessageId")
            return null
        }

        var finalLeafId: Long = startMessageId
        val visitedIds = mutableSetOf<Long>() // To detect cycles and prevent infinite loops

        while (currentPathMessage?.childrenMessageIds?.isNotEmpty() == true) {
            if (!visitedIds.add(currentPathMessage.id)) {
                // Cycle detected
                println("Warning: Cycle detected in message thread path at message ID: ${currentPathMessage.id}. Aborting traversal.")
                break
            }
            // Select the first child to traverse down
            val firstChildId = currentPathMessage.childrenMessageIds.first()
            currentPathMessage = messageMap[firstChildId]
            if (currentPathMessage == null) {
                // Data inconsistency: a child ID exists but the message is not in the map
                println("Warning: Child message $firstChildId not found during branch traversal. Using last valid message as leaf.")
                break
            }
            finalLeafId = currentPathMessage.id
        }
        return finalLeafId
    }

    /**
     * Utility function to build the list of messages for a specific branch (E1.S5).
     * Operates on the flat list of messages provided.
     *
     * @param allMessages The flat list of all messages in the session.
     * @param leafId The ID of the desired leaf message for the branch.
     * @return An ordered list of messages from the root of the branch down to the leaf.
     */
    private fun buildThreadBranch(allMessages: List<ChatMessage>, leafId: Long?): List<ChatMessage> {
        if (leafId == null || allMessages.isEmpty()) return emptyList()

        val messageMap = allMessages.associateBy { it.id }
        val branch = mutableListOf<ChatMessage>()
        var currentMessageId: Long? = leafId
        val visitedIds = mutableSetOf<Long>() // Added for cycle detection

        // Traverse upwards from the leaf to the root
        while (currentMessageId != null) {
            val message = messageMap[currentMessageId]
            if (message == null) {
                // This indicates a data inconsistency
                println("Warning: Could not find message with ID $currentMessageId while building branch. Aborting traversal.")
                return emptyList() // Return empty as the branch is incomplete/corrupt
            }
            if (!visitedIds.add(message.id)) {
                // Cycle detected during upward traversal
                println("Warning: Cycle detected in message thread path during upward traversal at message ID: ${message.id}. Aborting traversal.")
                return emptyList() // Return empty as the branch is corrupted
            }
            branch.add(message)
            currentMessageId = message.parentMessageId
        }

        // Reverse the list to get the correct order (root to leaf)
        return branch.reversed()
    }

    // Note: Copy Message (E3.S5) and Copy Branch (E2.S7) methods are UI-only and don't involve API calls,
    // so they would remain similar, operating on the displayedMessages StateFlow value.
    // Example:
    /*
    fun copyMessageContent(message: ChatMessage) {
       // Use ClipboardManager from Compose.current.clipboardManager
       // clipboardManager.setText(AnnotatedString(message.content))
    }

    fun copyVisibleBranchContent() {
       // Format displayedMessages.value into a single string
       // Use ClipboardManager to set the text
    }
    */
}

```

```
commit 505c3d7f06046951ef558ec90ab23f3136f39e8f
Author: --
Date:   Thu Aug 21 05:21:44 2025 +0200

    Add modular state management classes for `ChatViewModel`
    
    - Extract session handling into `SessionStateManager`.
    - Introduce `MessageInputStateManager` for input and editing state.
    - Implement `StreamingStateManager` to manage streaming message state.
    - Refactor `ChatViewModel` to leverage new modular managers for clearer separation of concerns.
    - Optimize `displayedMessages` with derived state management using `DerivedStateManager`.
    - Simplify dependencies and delegate message handling to dedicated handlers.

diff --git a/app/src/commonMain/composeResources/values/strings.xml b/app/src/commonMain/composeResources/values/strings.xml
index 1fde558..3f06089 100644
--- a/app/src/commonMain/composeResources/values/strings.xml
+++ b/app/src/commonMain/composeResources/values/strings.xml
@@ -18,7 +18,11 @@
     <string name="error_unknown">An unexpected error occurred.</string>
     <string name="error_loading_sessions_groups">Failed to load sessions or groups.</string>
     <string name="error_loading_session">Failed to load session.</string>
+    <string name="error_loading_session_short">Failed to load session</string>
     <string name="error_sending_message_short">Failed to send message</string>
+    <string name="error_updating_message_short">Failed to update message</string>
+    <string name="error_deleting_message_short">Failed to delete message</string>
+    <string name="error_switching_branch_short">Failed to switch branch</string>
 
     <!-- Input Area -->
     <string name="replying_to_prefix">Replying to:</string>
diff --git a/app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/ChatViewModel.kt b/app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/ChatViewModel.kt
index 9064513..caafb3d 100644
--- a/app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/ChatViewModel.kt
+++ b/app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/ChatViewModel.kt
@@ -3,37 +3,35 @@ package eu.torvian.chatbot.app.viewmodel
 import androidx.lifecycle.ViewModel
 import androidx.lifecycle.viewModelScope
 import eu.torvian.chatbot.app.domain.contracts.UiState
-import eu.torvian.chatbot.app.domain.events.SnackbarInteractionEvent
-import eu.torvian.chatbot.app.domain.events.apiRequestError
-import eu.torvian.chatbot.app.generated.resources.Res
-import eu.torvian.chatbot.app.generated.resources.error_loading_session
-import eu.torvian.chatbot.app.generated.resources.error_sending_message_short
 import eu.torvian.chatbot.app.service.api.ChatApi
 import eu.torvian.chatbot.app.service.api.SessionApi
 import eu.torvian.chatbot.app.service.api.SettingsApi
 import eu.torvian.chatbot.app.service.misc.EventBus
-import eu.torvian.chatbot.app.utils.misc.kmpLogger
+import eu.torvian.chatbot.app.viewmodel.chat.*
 import eu.torvian.chatbot.common.api.ApiError
-import eu.torvian.chatbot.common.models.*
+import eu.torvian.chatbot.common.models.ChatMessage
+import eu.torvian.chatbot.common.models.ChatSession
+import eu.torvian.chatbot.common.models.UpdateSessionModelRequest
+import eu.torvian.chatbot.common.models.UpdateSessionSettingsRequest
 import kotlinx.coroutines.CoroutineDispatcher
 import kotlinx.coroutines.CoroutineScope
 import kotlinx.coroutines.Dispatchers
-import kotlinx.coroutines.flow.*
+import kotlinx.coroutines.flow.StateFlow
 import kotlinx.coroutines.launch
 import kotlinx.datetime.Clock
-import org.jetbrains.compose.resources.getString
 
 /**
- * Manages the UI state for the main chat area of the currently active session,
- * using KMP ViewModel, StateFlow, Arrow's Either, and UiState for loading states.
+ * Manages the UI state for the main chat area of the currently active session.
  *
- * This class is responsible for:
- * - Holding the state (Idle/Loading/Success/Error) of the current session and its messages.
- * - Structuring the flat list of messages into a threaded view for display using Flows.
- * - Managing the state of the message input area.
- * - Handling user actions like sending messages, replying, editing, and deleting messages.
- * - Communicating with the backend via the ChatApi and SessionApi, handling their Either results.
- * - Managing the currently displayed thread branch based on the session's leaf message state.
+ * This refactored version uses a modular architecture with separate managers for different concerns:
+ * - SessionStateManager: Handles session loading and state management
+ * - MessageInputStateManager: Manages input, editing, and reply state
+ * - StreamingStateManager: Handles streaming message state
+ * - ThreadBranchManager: Manages thread branch operations
+ * - MessageProcessingHandler: Handles non-streaming message operations
+ * - StreamingMessageHandler: Handles streaming message operations
+ * - ChatViewModelErrorHandler: Centralized error handling
+ * - DerivedStateManager: Manages computed state for UI consumption
  *
  * @constructor
  * @param sessionApi The API client for session-related operations.
@@ -60,116 +58,97 @@ class ChatViewModel(
     private val clock: Clock = Clock.System
 ) : ViewModel() {
 
-    private val logger = kmpLogger<ChatViewModel>()
+    // --- Component Initialization ---
 
-    // --- Observable State for Compose UI (using StateFlow) ---
+    private val errorHandler = ChatViewModelErrorHandler(eventBus)
 
-    private val _sessionState = MutableStateFlow<UiState<ApiError, ChatSession>>(UiState.Idle)
+    private val sessionStateManager = SessionStateManager(
+        sessionApi = sessionApi,
+        scope = viewModelScope,
+        dispatcher = uiDispatcher,
+        clock = clock
+    )
+
+    private val inputStateManager = MessageInputStateManager()
+
+    private val streamingStateManager = StreamingStateManager()
+
+    private val threadBranchManager = ThreadBranchManager()
+
+    private val derivedStateManager = DerivedStateManager(
+        sessionStateManager = sessionStateManager,
+        streamingStateManager = streamingStateManager,
+        threadBranchManager = threadBranchManager,
+        scope = CoroutineScope(viewModelScope.coroutineContext + uiDispatcher)
+    )
+
+    private val messageProcessingHandler = MessageProcessingHandler(
+        chatApi = chatApi,
+        sessionStateManager = sessionStateManager,
+        inputStateManager = inputStateManager,
+        errorHandler = errorHandler,
+        scope = viewModelScope,
+        dispatcher = uiDispatcher
+    )
+
+    private val streamingMessageHandler = StreamingMessageHandler(
+        chatApi = chatApi,
+        sessionStateManager = sessionStateManager,
+        inputStateManager = inputStateManager,
+        streamingStateManager = streamingStateManager,
+        errorHandler = errorHandler,
+        scope = viewModelScope,
+        dispatcher = uiDispatcher
+    )
+
+    // --- Public State Properties (Delegated to Managers) ---
 
     /**
      * The state of the currently loaded chat session, including all messages.
      * When in Success state, provides the ChatSession object.
      */
-    val sessionState: StateFlow<UiState<ApiError, ChatSession>> = _sessionState.asStateFlow()
-
-    private val _currentBranchLeafId = MutableStateFlow<Long?>(null)
+    val sessionState: StateFlow<UiState<ApiError, ChatSession>> = sessionStateManager.sessionState
 
     /**
      * The ID of the leaf message in the currently displayed thread branch.
      * Changing this triggers the UI to show a different branch.
      * Null if the session is empty or not loaded/successful.
      */
-    val currentBranchLeafId: StateFlow<Long?> = _currentBranchLeafId.asStateFlow()
-
-    // New state to hold the actively streaming assistant message
-    private val _streamingAssistantMessage = MutableStateFlow<ChatMessage.AssistantMessage?>(null)
-
-    // New state to hold the temporary user message during streaming
-    private val _streamingUserMessage = MutableStateFlow<ChatMessage.UserMessage?>(null)
+    val currentBranchLeafId: StateFlow<Long?> = sessionStateManager.currentBranchLeafId
 
     /**
      * The list of messages to display in the UI, representing the currently selected thread branch.
      * This is derived from the session's full list of messages and the current leaf message ID,
      * combined with any actively streaming message.
      */
-    val displayedMessages: StateFlow<List<ChatMessage>> = combine(
-        _sessionState.filterIsInstance<UiState.Success<ChatSession>>()
-            .map { it.data.messages },
-        _currentBranchLeafId,
-        _streamingUserMessage,
-        _streamingAssistantMessage
-    ) { allPersistedMessages, leafId, streamingUserMessage, streamingAssistantMessage ->
-        val messagesForBranching = allPersistedMessages + listOfNotNull(streamingUserMessage, streamingAssistantMessage)
-        buildThreadBranch(messagesForBranching, leafId)
-    }
-        .stateIn(
-            scope = CoroutineScope(viewModelScope.coroutineContext + uiDispatcher),
-            started = SharingStarted.Eagerly,
-            initialValue = emptyList()
-        )
-
-    private val _inputContent = MutableStateFlow("")
+    val displayedMessages: StateFlow<List<ChatMessage>> = derivedStateManager.displayedMessages
 
     /**
      * The current text content in the message input field.
      */
-    val inputContent: StateFlow<String> = _inputContent.asStateFlow()
-
-    private val _replyTargetMessage = MutableStateFlow<ChatMessage?>(null)
+    val inputContent: StateFlow<String> = inputStateManager.inputContent
 
     /**
      * The message the user is currently explicitly replying to via the Reply action (E1.S7).
      * If null, sending a message replies to the [currentBranchLeafId] value.
      */
-    val replyTargetMessage: StateFlow<ChatMessage?> = _replyTargetMessage.asStateFlow()
-
-    private val _editingMessage = MutableStateFlow<ChatMessage?>(null)
+    val replyTargetMessage: StateFlow<ChatMessage?> = inputStateManager.replyTargetMessage
 
     /**
      * The message currently being edited (E3.S1, E3.S2). Null if no message is being edited.
      */
-    val editingMessage: StateFlow<ChatMessage?> = _editingMessage.asStateFlow()
-
-    private val _editingContent = MutableStateFlow("")
+    val editingMessage: StateFlow<ChatMessage?> = inputStateManager.editingMessage
 
     /**
      * The content of the message currently being edited (E3.S1, E3.S2).
      */
-    val editingContent: StateFlow<String> = _editingContent.asStateFlow()
-
-    private val _isSendingMessage = MutableStateFlow(false)
+    val editingContent: StateFlow<String> = inputStateManager.editingContent
 
     /**
      * Indicates whether a message is currently in the process of being sent. (E1.S3)
      */
-    val isSendingMessage: StateFlow<Boolean> = _isSendingMessage.asStateFlow()
-
-    // Store the ID of the last emitted error if a retry is possible
-    private val _lastFailedLoadEventId = MutableStateFlow<String?>(null)
+    val isSendingMessage: StateFlow<Boolean> = inputStateManager.isSendingMessage
 
-    // Store the session ID for retry functionality
-    private val _lastAttemptedSessionId = MutableStateFlow<Long?>(null)
-
-    init {
-        // ViewModel can listen to the EventBus for its own emitted event's responses
-        viewModelScope.launch(uiDispatcher) {
-            eventBus.events.collect { event ->
-                if (event is SnackbarInteractionEvent && event.originalAppEventId == _lastFailedLoadEventId.value) {
-                    if (event.isActionPerformed) {
-                        logger.info("Retrying loadSession due to Snackbar action!")
-                        _lastFailedLoadEventId.value = null // Clear ID before retrying
-                        _lastAttemptedSessionId.value?.let { sessionId ->
-                            loadSession(sessionId, forceReload = true) // Trigger retry
-                        }
-                    } else { // It was dismissed (by user or timeout)
-                        logger.info("Snackbar dismissed, not retrying loadSession.")
-                        _lastFailedLoadEventId.value = null
-                        _lastAttemptedSessionId.value = null
-                    }
-                }
-            }
-        }
-    }
 
     // --- Public Action Functions (Called by UI Components) ---
 
@@ -181,48 +160,25 @@ class ChatViewModel(
      * @param forceReload If true, reloads the session even if it's already loaded successfully.
      */
     fun loadSession(sessionId: Long?, forceReload: Boolean = false) {
-        // Prevent reloading if already loading or if the session is already loaded successfully
-        val currentState = _sessionState.value
-        if (!forceReload && (currentState.isLoading || (currentState.dataOrNull?.id == sessionId))) return
-
         if (sessionId == null) {
             clearSession()
             return
         }
 
-        // Store the session ID for potential retry
-        _lastAttemptedSessionId.value = sessionId
+        // Prevent reloading if already loading or if the session is already loaded successfully
+        val currentState = sessionStateManager.sessionState.value
+        if (!forceReload && (currentState.isLoading || (currentState.dataOrNull?.id == sessionId))) return
 
-        viewModelScope.launch(uiDispatcher) {
-            _sessionState.value = UiState.Loading
-            _replyTargetMessage.value = null
-            _editingMessage.value = null
-            _currentBranchLeafId.value = null
+        // Clear input and editing state when loading a new session
+        inputStateManager.clearInputContent()
+        inputStateManager.clearReplyTarget()
+        inputStateManager.cancelEditing()
+        streamingMessageHandler.cancelStreaming()
 
-            sessionApi.getSessionDetails(sessionId)
-                .fold(
-                    ifLeft = { error ->
-                        // Handle Error case (E1.S6)
-                        _sessionState.value = UiState.Error(error)
-                        // Emit to generic EventBus using the specific error type
-                        val globalError = apiRequestError(
-                            apiError = error,
-                            shortMessage = getString(Res.string.error_loading_session),
-                            isRetryable = true
-                        )
-                        _lastFailedLoadEventId.value = globalError.eventId // Store its ID
-                        eventBus.emitEvent(globalError)
-                    },
-                    ifRight = { session ->
-                        // Handle Success case (E2.S4)
-                        _sessionState.value = UiState.Success(session) // Success payload is the ChatSession
-                        // Set initial leaf to the session's saved leaf ID or null if no messages
-                        _currentBranchLeafId.value = session.currentLeafMessageId
-                        // Clear retry state on success
-                        _lastFailedLoadEventId.value = null
-                        _lastAttemptedSessionId.value = null
-                    }
-                )
+        sessionStateManager.loadSession(sessionId) { error ->
+            viewModelScope.launch(uiDispatcher) {
+                errorHandler.handleSessionLoadError(error, sessionId)
+            }
         }
     }
 
@@ -231,12 +187,12 @@ class ChatViewModel(
      * Called when the selected session is deleted or potentially on app exit.
      */
     fun clearSession() {
-        _sessionState.value = UiState.Idle // Go back to idle/no session state
-        _replyTargetMessage.value = null
-        _editingMessage.value = null
-        _currentBranchLeafId.value = null
-        _streamingAssistantMessage.value = null
-        _streamingUserMessage.value = null
+        inputStateManager.clearInputContent()
+        inputStateManager.clearReplyTarget()
+        inputStateManager.cancelEditing()
+        streamingMessageHandler.cancelStreaming()
+        // Note: We don't directly clear session state here as it's managed by SessionStateManager
+        // The UI should handle transitioning to idle state when no session is selected
     }
 
     /**
@@ -245,7 +201,7 @@ class ChatViewModel(
      * @param newText The new text from the input field.
      */
     fun updateInput(newText: String) {
-        _inputContent.value = newText
+        inputStateManager.updateInputContent(newText)
     }
 
     /**
@@ -254,156 +210,22 @@ class ChatViewModel(
      * (E1.S1, E1.S7)
      */
     fun sendMessage() {
-        val currentSession = _sessionState.value.dataOrNull ?: return // Cannot send if no session loaded successfully
-        val content = _inputContent.value.trim()
-        if (content.isBlank()) return // Cannot send empty message
+        val currentSession = sessionStateManager.currentSession ?: return
+        if (!inputStateManager.isInputContentValid()) return
 
-        val parentId = _replyTargetMessage.value?.id ?: _currentBranchLeafId.value // Use value from StateFlow
-
-        viewModelScope.launch(uiDispatcher) {
-            _isSendingMessage.value = true // Set sending state to true (E1.S3)
+        val content = inputStateManager.getTrimmedInputContent()
+        val parentId = inputStateManager.getParentMessageId(sessionStateManager.currentBranchLeafId.value)
 
-            try {
-                // Check if streaming is enabled in settings
-                val isStreamingEnabled = true // TODO: Implement settings check
+        // Check if streaming is enabled in settings
+        val isStreamingEnabled = true // TODO: Implement settings check
 
-                if (isStreamingEnabled) {
-                    handleStreamingMessage(currentSession, content, parentId)
-                } else {
-                    handleNonStreamingMessage(currentSession, content, parentId)
-                }
-            } finally {
-                _isSendingMessage.value = false // Always reset sending state
-            }
+        if (isStreamingEnabled) {
+            streamingMessageHandler.processStreamingMessage(currentSession, content, parentId)
+        } else {
+            messageProcessingHandler.processNewMessage(currentSession, content, parentId)
         }
     }
 
-    /**
-     * Handles streaming message processing.
-     */
-    private suspend fun handleStreamingMessage(currentSession: ChatSession, content: String, parentId: Long?) {
-        // Clear any previous streaming state
-        _streamingUserMessage.value = null
-        _streamingAssistantMessage.value = null
-
-        chatApi.processNewMessageStreaming(
-            sessionId = currentSession.id,
-            request = ProcessNewMessageRequest(content = content, parentMessageId = parentId)
-        ).collect { eitherUpdate ->
-            eitherUpdate.fold(
-                ifLeft = { error ->
-                    logger.error("Streaming message API error: ${error.code} - ${error.message}")
-                    // Clear any streaming state and emit error
-                    _streamingAssistantMessage.value = null
-                    _streamingUserMessage.value = null
-                    eventBus.emitEvent(
-                        apiRequestError(
-                            apiError = error,
-                            shortMessage = getString(Res.string.error_sending_message_short),
-                        )
-                    )
-                },
-                ifRight = { chatUpdate ->
-                    when (chatUpdate) {
-                        is ChatStreamEvent.UserMessageSaved -> {
-                            // Store the user message in the temporary streaming state
-                            _streamingUserMessage.value = chatUpdate.message
-                            _currentBranchLeafId.value = chatUpdate.message.id
-                            // Clear input and reply target after user message is confirmed
-                            _inputContent.value = ""
-                            _replyTargetMessage.value = null
-                        }
-
-                        is ChatStreamEvent.AssistantMessageStart -> {// Use the assistant message directly from the update
-                            _currentBranchLeafId.value = chatUpdate.assistantMessage.id
-                            _streamingAssistantMessage.value = chatUpdate.assistantMessage
-                        }
-
-                        is ChatStreamEvent.AssistantMessageDelta -> {// Update the streaming message content
-                            _streamingAssistantMessage.value?.let { currentStreamingMessage ->
-                                _streamingAssistantMessage.value = currentStreamingMessage.copy(
-                                    content = currentStreamingMessage.content + chatUpdate.deltaContent
-                                )
-                            }
-                        }
-
-                        is ChatStreamEvent.AssistantMessageEnd -> {
-                            // Update the session with the new messages
-                            val updatedMessages =
-                                currentSession.messages + chatUpdate.finalUserMessage + chatUpdate.finalAssistantMessage
-                            val newLeafId = chatUpdate.finalAssistantMessage.id
-
-                            _sessionState.value = UiState.Success(
-                                currentSession.copy(
-                                    messages = updatedMessages,
-                                    currentLeafMessageId = newLeafId,
-                                    updatedAt = clock.now()
-                                )
-                            )
-                            _currentBranchLeafId.value = newLeafId
-                            _streamingAssistantMessage.value = null // Clear streaming state
-                            _streamingUserMessage.value = null // Clear streaming user message
-                        }
-
-                        is ChatStreamEvent.ErrorOccurred -> {
-                            // Handle error during streaming
-                            logger.error("Streaming error: ${chatUpdate.error.message}")
-                            _streamingAssistantMessage.value = null
-                            _streamingUserMessage.value = null
-                            eventBus.emitEvent(
-                                apiRequestError(
-                                    apiError = chatUpdate.error,
-                                    shortMessage = getString(Res.string.error_sending_message_short),
-                                )
-                            )
-                        }
-
-                        ChatStreamEvent.StreamCompleted -> {
-                            // Streaming completed successfully
-                            logger.info("Streaming completed for session ${currentSession.id}")
-                        }
-                    }
-                }
-            )
-        }
-    }
-
-    /**
-     * Handles non-streaming message processing.
-     */
-    private suspend fun handleNonStreamingMessage(currentSession: ChatSession, content: String, parentId: Long?) {
-        chatApi.processNewMessage(
-            sessionId = currentSession.id,
-            request = ProcessNewMessageRequest(content = content, parentMessageId = parentId)
-        ).fold(
-            ifLeft = { error ->
-                logger.error("Send message API error: ${error.code} - ${error.message}")
-                eventBus.emitEvent(
-                    apiRequestError(
-                        apiError = error,
-                        shortMessage = getString(Res.string.error_sending_message_short),
-                    )
-                )
-            },
-            ifRight = { newMessages ->
-                // Add the new messages to the current session's messages
-                val updatedMessages = currentSession.messages + newMessages
-                val newLeafId = newMessages.lastOrNull()?.id
-
-                // Update the session object inside the Success state with the new messages
-                _sessionState.value = UiState.Success(
-                    currentSession.copy(
-                        messages = updatedMessages,
-                        currentLeafMessageId = newLeafId,
-                        updatedAt = clock.now()
-                    )
-                )
-                _currentBranchLeafId.value = newLeafId
-                _replyTargetMessage.value = null
-                _inputContent.value = ""
-            }
-        )
-    }
 
     /**
      * Sets the state to indicate the user is replying to a specific message (E1.S7).
@@ -411,15 +233,14 @@ class ChatViewModel(
      * @param message The message to reply to.
      */
     fun startReplyTo(message: ChatMessage) {
-        _replyTargetMessage.value = message
-        // Optionally, trigger scroll action in UI
+        inputStateManager.setReplyTarget(message)
     }
 
     /**
      * Cancels the specific reply target, reverting to replying to the current leaf (E1.S7).
      */
     fun cancelReply() {
-        _replyTargetMessage.value = null
+        inputStateManager.clearReplyTarget()
     }
 
     /**
@@ -428,8 +249,7 @@ class ChatViewModel(
      * @param message The message to edit.
      */
     fun startEditing(message: ChatMessage) {
-        _editingMessage.value = message
-        _editingContent.value = message.content
+        inputStateManager.startEditing(message)
     }
 
     /**
@@ -439,57 +259,33 @@ class ChatViewModel(
      * @param newText The new text content for the editing field.
      */
     fun updateEditingContent(newText: String) {
-        _editingContent.value = newText
+        inputStateManager.updateEditingContent(newText)
     }
 
     /**
      * Saves the edited message content (E3.S3).
      */
     fun saveEditing() {
-        val messageToEdit = _editingMessage.value ?: return
-        val newContent = _editingContent.value.trim()
-        if (newContent.isBlank()) {
-            // Show inline validation error (UI concern) or update state
-            println("Validation Error: Message content cannot be empty.")
+        val messageToEdit = inputStateManager.editingMessage.value ?: return
+        if (!inputStateManager.isEditingContentValid()) {
+            errorHandler.handleValidationError("Message content cannot be empty", "save editing")
             return
         }
-        val currentSession = _sessionState.value.dataOrNull ?: return
 
-        viewModelScope.launch(uiDispatcher) {
-            // Optionally show inline loading/saving state for the specific message being edited
-            chatApi.updateMessageContent(messageToEdit.id, UpdateMessageRequest(newContent))
-                .fold(
-                    ifLeft = { error ->
-                        println("Edit message API error: ${error.code} - ${error.message}")
-                        // Show inline error for the edited message (UI concern)
-                    },
-                    ifRight = { updatedMessage ->
-                        // Update the message in the messages list within the current session state Flow (E3.S3)
-                        val updatedAllMessages = currentSession.messages.map {
-                            if (it.id == updatedMessage.id) updatedMessage else it
-                        }
-                        _sessionState.value = UiState.Success(
-                            currentSession.copy(
-                                messages = updatedAllMessages,
-                                updatedAt = clock.now()
-                            )
-                        )
-                        // displayedMessages StateFlow derived state will react
-
-                        // Clear editing state on success
-                        _editingMessage.value = null
-                        _editingContent.value = ""
-                    }
-                )
-        }
+        val newContent = inputStateManager.getTrimmedEditingContent()
+        messageProcessingHandler.updateMessageContent(
+            messageId = messageToEdit.id,
+            newContent = newContent,
+            onSuccess = { inputStateManager.completeEditing() },
+            onError = { /* Error already handled by messageProcessingHandler */ }
+        )
     }
 
     /**
      * Cancels the message editing state (E3.S1, E3.S2).
      */
     fun cancelEditing() {
-        _editingMessage.value = null
-        _editingContent.value = ""
+        inputStateManager.cancelEditing()
     }
 
     /**
@@ -498,23 +294,16 @@ class ChatViewModel(
      * @param messageId The ID of the message to delete.
      */
     fun deleteMessage(messageId: Long) {
-        val currentSession = _sessionState.value.dataOrNull ?: return
-        viewModelScope.launch(uiDispatcher) {
-            // Optionally show inline loading state for the specific message being deleted
-            chatApi.deleteMessage(messageId)
-                .fold(
-                    ifLeft = { error ->
-                        println("Delete message API error: ${error.code} - ${error.message}")
-                        // Show transient error message
-                    },
-                    ifRight = {
-                        // Backend handled deletion recursively (E3.S4).
-                        // Reload the session to update the UI state correctly (V1.1 strategy).
-                        // This action launches a new coroutine within viewModelScope.
-                        loadSession(currentSession.id, forceReload = true)
-                    }
-                )
-        }
+        messageProcessingHandler.deleteMessage(
+            messageId = messageId,
+            onSuccess = {
+                // Reload the session to update the UI state correctly
+                sessionStateManager.currentSession?.let { session ->
+                    loadSession(session.id, forceReload = true)
+                }
+            },
+            onError = { /* Error already handled by messageProcessingHandler */ }
+        )
     }
 
     /**
@@ -528,41 +317,32 @@ class ChatViewModel(
      *                        a root, middle, or leaf message in the conversation tree.
      */
     fun switchBranchToMessage(targetMessageId: Long) {
-        val currentSession = _sessionState.value.dataOrNull ?: return
-        if (_currentBranchLeafId.value == targetMessageId) return
+        val currentSession = sessionStateManager.currentSession ?: return
+        if (sessionStateManager.currentBranchLeafId.value == targetMessageId) return
 
         val messageMap = currentSession.messages.associateBy { it.id }
 
-        // Use the new helper function to find the actual leaf ID
-        val finalLeafId = findLeafOfBranch(targetMessageId, messageMap)
+        // Use the thread branch manager to find the actual leaf ID
+        val finalLeafId = threadBranchManager.findLeafOfBranch(targetMessageId, messageMap)
         if (finalLeafId == null) {
-            println("Warning: Could not determine a valid leaf for branch starting with $targetMessageId.")
+            errorHandler.handleDataConsistencyError(
+                "Could not determine a valid leaf for branch starting with $targetMessageId",
+                currentSession.id,
+                targetMessageId
+            )
             return
         }
 
-        if (_currentBranchLeafId.value == finalLeafId) return // Already on this exact branch
-
-        viewModelScope.launch(uiDispatcher) {
-            // Optimistically update UI state Flow first for responsiveness
-            _currentBranchLeafId.value = finalLeafId
+        if (sessionStateManager.currentBranchLeafId.value == finalLeafId) return // Already on this exact branch
 
-            // Persist the change to the backend (E1.S5 requirement)
-            sessionApi.updateSessionLeafMessage(currentSession.id, UpdateSessionLeafMessageRequest(finalLeafId))
-                .fold(
-                    ifLeft = { error ->
-                        println("Update leaf message API error: ${error.code} - ${error.message}")
-                        // Decide rollback strategy if needed, or just show a transient error message.
-                    },
-                    ifRight = {
-                        // Persistence successful.
-                        // We should also update the leafMessageId in the session object itself
-                        // within the state Flow to keep the ChatSession data consistent.
-                        _sessionState.value = UiState.Success(
-                            currentSession.copy(currentLeafMessageId = finalLeafId)
-                        )
-                    }
-                )
-        }
+        sessionStateManager.updateSessionLeafMessage(
+            leafId = finalLeafId,
+            onError = { error ->
+                viewModelScope.launch(uiDispatcher) {
+                    errorHandler.handleBranchSwitchError(error, currentSession.id, targetMessageId)
+                }
+            }
+        )
     }
 
     /**
@@ -571,19 +351,17 @@ class ChatViewModel(
      * @param modelId The ID of the model to select, or null to unset.
      */
     fun selectModel(modelId: Long?) {
-        val currentSession = _sessionState.value.dataOrNull ?: return
+        val currentSession = sessionStateManager.currentSession ?: return
         viewModelScope.launch(uiDispatcher) {
             sessionApi.updateSessionModel(currentSession.id, UpdateSessionModelRequest(modelId))
                 .fold(
                     ifLeft = { error ->
-                        println("Update session model API error: ${error.code} - ${error.message}")
-                        // Show error
+                        errorHandler.handleSessionLoadError(error, currentSession.id, "update session model")
                     },
                     ifRight = {
                         // Update the session object within the state Flow manually as backend doesn't return it
-                        _sessionState.value = UiState.Success(
-                            currentSession.copy(currentModelId = modelId)
-                        )
+                        val updatedSession = currentSession.copy(currentModelId = modelId)
+                        sessionStateManager.updateSession(updatedSession)
                     }
                 )
         }
@@ -595,98 +373,22 @@ class ChatViewModel(
      * @param settingsId The ID of the settings profile to select, or null to unset.
      */
     fun selectSettings(settingsId: Long?) {
-        val currentSession = _sessionState.value.dataOrNull ?: return
+        val currentSession = sessionStateManager.currentSession ?: return
         viewModelScope.launch(uiDispatcher) {
             sessionApi.updateSessionSettings(currentSession.id, UpdateSessionSettingsRequest(settingsId))
                 .fold(
                     ifLeft = { error ->
-                        println("Update session settings API error: ${error.code} - ${error.message}")
-                        // Show error
+                        errorHandler.handleSessionLoadError(error, currentSession.id, "update session settings")
                     },
                     ifRight = {
                         // Update the session object within the state Flow manually
-                        _sessionState.value = UiState.Success(
-                            currentSession.copy(currentSettingsId = settingsId)
-                        )
+                        val updatedSession = currentSession.copy(currentSettingsId = settingsId)
+                        sessionStateManager.updateSession(updatedSession)
                     }
                 )
         }
     }
 
-    /**
-     * Finds the ultimate leaf message ID by traversing down the
-     * first child path from a given starting message ID.
-     *
-     * @param startMessageId The ID of the message to start the traversal from.
-     * @param messageMap A map of all messages in the session for efficient lookup.
-     * @return The ID of the leaf message found, or null if the startMessageId is invalid or a cycle/broken link is detected.
-     */
-    private fun findLeafOfBranch(startMessageId: Long, messageMap: Map<Long, ChatMessage>): Long? {
-        var currentPathMessage: ChatMessage? = messageMap[startMessageId]
-        if (currentPathMessage == null) {
-            println("Warning: Starting message for branch traversal not found: $startMessageId")
-            return null
-        }
-
-        var finalLeafId: Long = startMessageId
-        val visitedIds = mutableSetOf<Long>() // To detect cycles and prevent infinite loops
-
-        while (currentPathMessage?.childrenMessageIds?.isNotEmpty() == true) {
-            if (!visitedIds.add(currentPathMessage.id)) {
-                // Cycle detected
-                println("Warning: Cycle detected in message thread path at message ID: ${currentPathMessage.id}. Aborting traversal.")
-                break
-            }
-            // Select the first child to traverse down
-            val firstChildId = currentPathMessage.childrenMessageIds.first()
-            currentPathMessage = messageMap[firstChildId]
-            if (currentPathMessage == null) {
-                // Data inconsistency: a child ID exists but the message is not in the map
-                println("Warning: Child message $firstChildId not found during branch traversal. Using last valid message as leaf.")
-                break
-            }
-            finalLeafId = currentPathMessage.id
-        }
-        return finalLeafId
-    }
-
-    /**
-     * Utility function to build the list of messages for a specific branch (E1.S5).
-     * Operates on the flat list of messages provided.
-     *
-     * @param allMessages The flat list of all messages in the session.
-     * @param leafId The ID of the desired leaf message for the branch.
-     * @return An ordered list of messages from the root of the branch down to the leaf.
-     */
-    private fun buildThreadBranch(allMessages: List<ChatMessage>, leafId: Long?): List<ChatMessage> {
-        if (leafId == null || allMessages.isEmpty()) return emptyList()
-
-        val messageMap = allMessages.associateBy { it.id }
-        val branch = mutableListOf<ChatMessage>()
-        var currentMessageId: Long? = leafId
-        val visitedIds = mutableSetOf<Long>() // Added for cycle detection
-
-        // Traverse upwards from the leaf to the root
-        while (currentMessageId != null) {
-            val message = messageMap[currentMessageId]
-            if (message == null) {
-                // This indicates a data inconsistency
-                println("Warning: Could not find message with ID $currentMessageId while building branch. Aborting traversal.")
-                return emptyList() // Return empty as the branch is incomplete/corrupt
-            }
-            if (!visitedIds.add(message.id)) {
-                // Cycle detected during upward traversal
-                println("Warning: Cycle detected in message thread path during upward traversal at message ID: ${message.id}. Aborting traversal.")
-                return emptyList() // Return empty as the branch is corrupted
-            }
-            branch.add(message)
-            currentMessageId = message.parentMessageId
-        }
-
-        // Reverse the list to get the correct order (root to leaf)
-        return branch.reversed()
-    }
-
     // Note: Copy Message (E3.S5) and Copy Branch (E2.S7) methods are UI-only and don't involve API calls,
     // so they would remain similar, operating on the displayedMessages StateFlow value.
     // Example:
diff --git a/app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/chat/ChatViewModelErrorHandler.kt b/app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/chat/ChatViewModelErrorHandler.kt
new file mode 100644
index 0000000..501d04f
--- /dev/null
+++ b/app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/chat/ChatViewModelErrorHandler.kt
@@ -0,0 +1,196 @@
+package eu.torvian.chatbot.app.viewmodel.chat
+
+import eu.torvian.chatbot.app.domain.events.apiRequestError
+import eu.torvian.chatbot.app.generated.resources.*
+import eu.torvian.chatbot.app.service.misc.EventBus
+import eu.torvian.chatbot.app.utils.misc.kmpLogger
+import eu.torvian.chatbot.common.api.ApiError
+import org.jetbrains.compose.resources.getString
+
+/**
+ * Centralized error handling for ChatViewModel operations.
+ *
+ * This class provides consistent error handling patterns across all ChatViewModel operations,
+ * including logging, user notification via EventBus, and error recovery strategies.
+ */
+class ChatViewModelErrorHandler(
+    private val eventBus: EventBus
+) {
+
+    private val logger = kmpLogger<ChatViewModelErrorHandler>()
+
+    /**
+     * Handles errors that occur during session loading operations.
+     *
+     * @param error The API error that occurred
+     * @param sessionId The ID of the session that failed to load
+     * @param operation Optional description of the specific operation that failed
+     */
+    suspend fun handleSessionLoadError(
+        error: ApiError,
+        sessionId: Long,
+        operation: String = "load session"
+    ) {
+        logger.error("Session $operation error for session $sessionId: ${error.code} - ${error.message}")
+
+        eventBus.emitEvent(
+            apiRequestError(
+                apiError = error,
+                shortMessage = getString(Res.string.error_loading_session_short),
+            )
+        )
+    }
+
+    /**
+     * Handles errors that occur during message sending operations.
+     *
+     * @param error The API error that occurred
+     * @param sessionId The ID of the session where the message failed to send
+     * @param isStreaming Whether this was a streaming operation
+     */
+    suspend fun handleMessageSendError(
+        error: ApiError,
+        sessionId: Long,
+        isStreaming: Boolean = false
+    ) {
+        val operationType = if (isStreaming) "Streaming" else "Send"
+        logger.error("$operationType message API error for session $sessionId: ${error.code} - ${error.message}")
+
+        eventBus.emitEvent(
+            apiRequestError(
+                apiError = error,
+                shortMessage = getString(Res.string.error_sending_message_short),
+            )
+        )
+    }
+
+    /**
+     * Handles errors that occur during message update operations.
+     *
+     * @param error The API error that occurred
+     * @param messageId The ID of the message that failed to update
+     */
+    suspend fun handleMessageUpdateError(error: ApiError, messageId: Long) {
+        logger.error("Update message API error for message $messageId: ${error.code} - ${error.message}")
+
+        eventBus.emitEvent(
+            apiRequestError(
+                apiError = error,
+                shortMessage = getString(Res.string.error_updating_message_short),
+            )
+        )
+    }
+
+    /**
+     * Handles errors that occur during message deletion operations.
+     *
+     * @param error The API error that occurred
+     * @param messageId The ID of the message that failed to delete
+     */
+    suspend fun handleMessageDeleteError(error: ApiError, messageId: Long) {
+        logger.error("Delete message API error for message $messageId: ${error.code} - ${error.message}")
+
+        eventBus.emitEvent(
+            apiRequestError(
+                apiError = error,
+                shortMessage = getString(Res.string.error_deleting_message_short),
+            )
+        )
+    }
+
+    /**
+     * Handles errors that occur during branch switching operations.
+     *
+     * @param error The API error that occurred
+     * @param sessionId The ID of the session where branch switching failed
+     * @param targetMessageId The ID of the message that was the target of the branch switch
+     */
+    suspend fun handleBranchSwitchError(
+        error: ApiError,
+        sessionId: Long,
+        targetMessageId: Long
+    ) {
+        logger.error("Switch branch API error for session $sessionId, target message $targetMessageId: ${error.code} - ${error.message}")
+
+        eventBus.emitEvent(
+            apiRequestError(
+                apiError = error,
+                shortMessage = getString(Res.string.error_switching_branch_short),
+            )
+        )
+    }
+
+    /**
+     * Handles streaming-specific errors that occur during message streaming.
+     *
+     * @param error The API error that occurred during streaming
+     * @param sessionId The ID of the session where streaming failed
+     * @param streamingPhase Description of which phase of streaming failed
+     */
+    suspend fun handleStreamingError(
+        error: ApiError,
+        sessionId: Long,
+        streamingPhase: String = "unknown"
+    ) {
+        logger.error("Streaming error in phase '$streamingPhase' for session $sessionId: ${error.code} - ${error.message}")
+
+        eventBus.emitEvent(
+            apiRequestError(
+                apiError = error,
+                shortMessage = getString(Res.string.error_sending_message_short),
+            )
+        )
+    }
+
+    /**
+     * Handles validation errors that occur before API calls.
+     * These are typically client-side validation failures.
+     *
+     * @param validationMessage The validation error message
+     * @param operation The operation that failed validation
+     */
+    fun handleValidationError(validationMessage: String, operation: String) {
+        logger.warn("Validation error in $operation: $validationMessage")
+        // For validation errors, we typically don't emit to EventBus as they're user input issues
+        // The UI should handle these directly
+    }
+
+    /**
+     * Handles data consistency errors that occur during thread operations.
+     * These indicate potential issues with the message tree structure.
+     *
+     * @param errorMessage The error message describing the consistency issue
+     * @param sessionId The ID of the session where the issue occurred
+     * @param messageId The ID of the message involved in the issue (if applicable)
+     */
+    fun handleDataConsistencyError(
+        errorMessage: String,
+        sessionId: Long,
+        messageId: Long? = null
+    ) {
+        val messageInfo = messageId?.let { " (message $it)" } ?: ""
+        logger.error("Data consistency error in session $sessionId$messageInfo: $errorMessage")
+
+        // Data consistency errors are serious and should be logged but may not need user notification
+        // unless they prevent normal operation
+    }
+
+    /**
+     * Handles general unexpected errors that don't fit other categories.
+     *
+     * @param error The throwable that occurred
+     * @param operation The operation that was being performed
+     * @param context Additional context information
+     */
+    fun handleUnexpectedError(
+        error: Throwable,
+        operation: String,
+        context: String = ""
+    ) {
+        val contextInfo = if (context.isNotEmpty()) " ($context)" else ""
+        logger.error("Unexpected error during $operation$contextInfo", error)
+
+        // For unexpected errors, we might want to emit a generic error event
+        // but we need to be careful not to expose internal details to users
+    }
+}
diff --git a/app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/chat/DerivedStateManager.kt b/app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/chat/DerivedStateManager.kt
new file mode 100644
index 0000000..aec5f31
--- /dev/null
+++ b/app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/chat/DerivedStateManager.kt
@@ -0,0 +1,179 @@
+package eu.torvian.chatbot.app.viewmodel.chat
+
+import eu.torvian.chatbot.app.domain.contracts.UiState
+import eu.torvian.chatbot.common.api.ApiError
+import eu.torvian.chatbot.common.models.ChatMessage
+import eu.torvian.chatbot.common.models.ChatSession
+import kotlinx.coroutines.CoroutineScope
+import kotlinx.coroutines.flow.*
+
+/**
+ * Manages derived state calculations for the ChatViewModel.
+ *
+ * This class is responsible for:
+ * - Creating derived StateFlows from base state
+ * - Optimizing state calculations to avoid redundant work
+ * - Providing computed state for UI consumption
+ * - Managing complex state combinations
+ */
+class DerivedStateManager(
+    private val sessionStateManager: SessionStateManager,
+    private val streamingStateManager: StreamingStateManager,
+    private val threadBranchManager: ThreadBranchManager,
+    private val scope: CoroutineScope
+) {
+
+    /**
+     * The list of messages to display in the UI, representing the currently selected thread branch.
+     * This is derived from the session's full list of messages and the current leaf message ID,
+     * combined with any actively streaming message.
+     */
+    val displayedMessages: StateFlow<List<ChatMessage>> = combine(
+        sessionStateManager.sessionState.filterIsInstance<UiState.Success<ChatSession>>()
+            .map { it.data.messages },
+        sessionStateManager.currentBranchLeafId,
+        streamingStateManager.streamingUserMessage,
+        streamingStateManager.streamingAssistantMessage
+    ) { allPersistedMessages, leafId, streamingUserMessage, streamingAssistantMessage ->
+        buildDisplayedMessages(allPersistedMessages, leafId, streamingUserMessage, streamingAssistantMessage)
+    }.stateIn(
+        scope = scope,
+        started = SharingStarted.Eagerly,
+        initialValue = emptyList()
+    )
+
+    /**
+     * Indicates whether the UI should show a loading state for the session.
+     */
+    val isSessionLoading: StateFlow<Boolean> = sessionStateManager.sessionState.map { state ->
+        state is UiState.Loading
+    }.stateIn(scope, SharingStarted.Eagerly, false)
+
+    /**
+     * Indicates whether the session is in an error state.
+     */
+    val isSessionError: StateFlow<Boolean> = sessionStateManager.sessionState.map { state ->
+        state is UiState.Error
+    }.stateIn(scope, SharingStarted.Eagerly, false)
+
+    /**
+     * Gets the current session error if in error state.
+     */
+    val sessionError: StateFlow<ApiError?> = sessionStateManager.sessionState.map { state ->
+        (state as? UiState.Error)?.error
+    }.stateIn(scope, SharingStarted.Eagerly, null)
+
+    /**
+     * Indicates whether the session has been successfully loaded.
+     */
+    val isSessionLoaded: StateFlow<Boolean> = sessionStateManager.sessionState.map { state ->
+        state is UiState.Success
+    }.stateIn(scope, SharingStarted.Eagerly, false)
+
+    /**
+     * Indicates whether there are any messages to display.
+     */
+    val hasMessages: StateFlow<Boolean> = displayedMessages.map { messages ->
+        messages.isNotEmpty()
+    }.stateIn(scope, SharingStarted.Eagerly, false)
+
+    /**
+     * Gets the count of displayed messages.
+     */
+    val messageCount: StateFlow<Int> = displayedMessages.map { messages ->
+        messages.size
+    }.stateIn(scope, SharingStarted.Eagerly, 0)
+
+    /**
+     * Indicates whether streaming is currently active.
+     */
+    val isStreaming: StateFlow<Boolean> = combine(
+        streamingStateManager.streamingUserMessage,
+        streamingStateManager.streamingAssistantMessage
+    ) { userMessage, assistantMessage ->
+        userMessage != null || assistantMessage != null
+    }.stateIn(scope, SharingStarted.Eagerly, false)
+
+    /**
+     * Gets the current streaming content for display purposes.
+     */
+    val currentStreamingContent: StateFlow<String> = streamingStateManager.streamingAssistantMessage.map { message ->
+        message?.content ?: ""
+    }.stateIn(scope, SharingStarted.Eagerly, "")
+
+    /**
+     * Builds the list of messages to display, combining persisted messages with streaming messages.
+     *
+     * @param persistedMessages The persisted messages from the session
+     * @param leafId The current branch leaf ID
+     * @param streamingUserMessage The currently streaming user message (if any)
+     * @param streamingAssistantMessage The currently streaming assistant message (if any)
+     * @return The ordered list of messages to display
+     */
+    private fun buildDisplayedMessages(
+        persistedMessages: List<ChatMessage>,
+        leafId: Long?,
+        streamingUserMessage: ChatMessage.UserMessage?,
+        streamingAssistantMessage: ChatMessage.AssistantMessage?
+    ): List<ChatMessage> {
+        // Combine persisted messages with streaming messages
+        val allMessages = persistedMessages + listOfNotNull(streamingUserMessage, streamingAssistantMessage)
+
+        // If no leaf ID is specified, return empty list
+        if (leafId == null) return emptyList()
+
+        // Build the thread branch using the thread manager
+        return when (val result = threadBranchManager.buildThreadBranch(allMessages, leafId)) {
+            is ThreadBranchManager.ThreadBranchResult.Success -> result.messages
+            is ThreadBranchManager.ThreadBranchResult.Error -> {
+                // Log the error but return empty list to avoid UI crashes
+                // The error is already logged by ThreadBranchManager
+                emptyList()
+            }
+        }
+    }
+
+    /**
+     * Creates a derived state that combines multiple boolean states with AND logic.
+     *
+     * @param states The StateFlows to combine
+     * @return A StateFlow that emits true only when all input states are true
+     */
+    fun combineWithAnd(vararg states: StateFlow<Boolean>): StateFlow<Boolean> {
+        return combine(*states) { values ->
+            values.all { it }
+        }.stateIn(scope, SharingStarted.Eagerly, false)
+    }
+
+    /**
+     * Creates a derived state that combines multiple boolean states with OR logic.
+     *
+     * @param states The StateFlows to combine
+     * @return A StateFlow that emits true when any input state is true
+     */
+    fun combineWithOr(vararg states: StateFlow<Boolean>): StateFlow<Boolean> {
+        return combine(*states) { values ->
+            values.any { it }
+        }.stateIn(scope, SharingStarted.Eagerly, false)
+    }
+
+    /**
+     * Creates a derived state that indicates whether any operation is in progress.
+     * This includes session loading, message sending, and streaming.
+     */
+    val isAnyOperationInProgress: StateFlow<Boolean> = combineWithOr(
+        isSessionLoading,
+        isStreaming
+    )
+
+    /**
+     * Creates a derived state that indicates whether the UI should be interactive.
+     * The UI is interactive when a session is loaded and no critical operations are in progress.
+     */
+    val isUiInteractive: StateFlow<Boolean> = combine(
+        isSessionLoaded,
+        isAnyOperationInProgress
+    ) { sessionLoaded, operationInProgress ->
+        sessionLoaded && !operationInProgress
+    }.stateIn(scope, SharingStarted.Eagerly, false)
+}
diff --git a/app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/chat/MessageInputStateManager.kt b/app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/chat/MessageInputStateManager.kt
new file mode 100644
index 0000000..33fa801
--- /dev/null
+++ b/app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/chat/MessageInputStateManager.kt
@@ -0,0 +1,185 @@
+package eu.torvian.chatbot.app.viewmodel.chat
+
+import eu.torvian.chatbot.common.models.ChatMessage
+import kotlinx.coroutines.flow.MutableStateFlow
+import kotlinx.coroutines.flow.StateFlow
+import kotlinx.coroutines.flow.asStateFlow
+
+/**
+ * Manages the state of message input and editing operations.
+ *
+ * This class is responsible for:
+ * - Managing the current input content
+ * - Handling reply target state
+ * - Managing message editing state
+ * - Tracking sending state
+ */
+class MessageInputStateManager {
+
+    private val _inputContent = MutableStateFlow("")
+
+    /**
+     * The current text content in the message input field.
+     */
+    val inputContent: StateFlow<String> = _inputContent.asStateFlow()
+
+    private val _replyTargetMessage = MutableStateFlow<ChatMessage?>(null)
+
+    /**
+     * The message the user is currently explicitly replying to via the Reply action.
+     * Null if not replying to any specific message.
+     */
+    val replyTargetMessage: StateFlow<ChatMessage?> = _replyTargetMessage.asStateFlow()
+
+    private val _editingMessage = MutableStateFlow<ChatMessage?>(null)
+
+    /**
+     * The message currently being edited. Null if no message is being edited.
+     */
+    val editingMessage: StateFlow<ChatMessage?> = _editingMessage.asStateFlow()
+
+    private val _editingContent = MutableStateFlow("")
+
+    /**
+     * The content of the message currently being edited.
+     */
+    val editingContent: StateFlow<String> = _editingContent.asStateFlow()
+
+    private val _isSendingMessage = MutableStateFlow(false)
+
+    /**
+     * Indicates whether a message is currently in the process of being sent.
+     */
+    val isSendingMessage: StateFlow<Boolean> = _isSendingMessage.asStateFlow()
+
+    /**
+     * Updates the input content.
+     *
+     * @param newText The new text content for the input field
+     */
+    fun updateInputContent(newText: String) {
+        _inputContent.value = newText
+    }
+
+    /**
+     * Gets the current input content trimmed of whitespace.
+     *
+     * @return The trimmed input content
+     */
+    fun getTrimmedInputContent(): String {
+        return _inputContent.value.trim()
+    }
+
+    /**
+     * Checks if the current input content is valid (not blank after trimming).
+     *
+     * @return True if input is valid, false otherwise
+     */
+    fun isInputContentValid(): Boolean {
+        return getTrimmedInputContent().isNotBlank()
+    }
+
+    /**
+     * Clears the input content.
+     */
+    fun clearInputContent() {
+        _inputContent.value = ""
+    }
+
+    /**
+     * Sets the reply target message.
+     *
+     * @param message The message to reply to
+     */
+    fun setReplyTarget(message: ChatMessage) {
+        _replyTargetMessage.value = message
+    }
+
+    /**
+     * Clears the reply target.
+     */
+    fun clearReplyTarget() {
+        _replyTargetMessage.value = null
+    }
+
+    /**
+     * Gets the parent message ID for a new message.
+     * This will be the reply target ID if set, otherwise null.
+     *
+     * @param fallbackLeafId The fallback leaf ID to use if no reply target is set
+     * @return The parent message ID to use
+     */
+    fun getParentMessageId(fallbackLeafId: Long?): Long? {
+        return _replyTargetMessage.value?.id ?: fallbackLeafId
+    }
+
+    /**
+     * Starts editing a message.
+     *
+     * @param message The message to edit
+     */
+    fun startEditing(message: ChatMessage) {
+        _editingMessage.value = message
+        _editingContent.value = message.content
+    }
+
+    /**
+     * Updates the editing content.
+     *
+     * @param newText The new text content for the editing field
+     */
+    fun updateEditingContent(newText: String) {
+        _editingContent.value = newText
+    }
+
+    /**
+     * Gets the current editing content trimmed of whitespace.
+     *
+     * @return The trimmed editing content
+     */
+    fun getTrimmedEditingContent(): String {
+        return _editingContent.value.trim()
+    }
+
+    /**
+     * Checks if the current editing content is valid (not blank after trimming).
+     *
+     * @return True if editing content is valid, false otherwise
+     */
+    fun isEditingContentValid(): Boolean {
+        return getTrimmedEditingContent().isNotBlank()
+    }
+
+    /**
+     * Cancels the current editing operation.
+     */
+    fun cancelEditing() {
+        _editingMessage.value = null
+        _editingContent.value = ""
+    }
+
+    /**
+     * Completes the editing operation by clearing the editing state.
+     */
+    fun completeEditing() {
+        _editingMessage.value = null
+        _editingContent.value = ""
+    }
+
+    /**
+     * Sets the sending state.
+     *
+     * @param isSending True if currently sending a message, false otherwise
+     */
+    fun setSendingState(isSending: Boolean) {
+        _isSendingMessage.value = isSending
+    }
+
+    /**
+     * Completes a successful message send by clearing input and reply target.
+     */
+    fun completeMessageSend() {
+        clearInputContent()
+        clearReplyTarget()
+    }
+}
diff --git a/app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/chat/MessageProcessingHandler.kt b/app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/chat/MessageProcessingHandler.kt
new file mode 100644
index 0000000..5310a65
--- /dev/null
+++ b/app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/chat/MessageProcessingHandler.kt
@@ -0,0 +1,121 @@
+package eu.torvian.chatbot.app.viewmodel.chat
+
+import eu.torvian.chatbot.app.service.api.ChatApi
+import eu.torvian.chatbot.common.models.ChatSession
+import eu.torvian.chatbot.common.models.ProcessNewMessageRequest
+import eu.torvian.chatbot.common.models.UpdateMessageRequest
+import kotlinx.coroutines.CoroutineDispatcher
+import kotlinx.coroutines.CoroutineScope
+import kotlinx.coroutines.launch
+
+/**
+ * Handles non-streaming message processing operations.
+ *
+ * This class is responsible for:
+ * - Processing new messages without streaming
+ * - Coordinating with state managers to update UI state
+ * - Handling errors during message processing
+ */
+class MessageProcessingHandler(
+    private val chatApi: ChatApi,
+    private val sessionStateManager: SessionStateManager,
+    private val inputStateManager: MessageInputStateManager,
+    private val errorHandler: ChatViewModelErrorHandler,
+    private val scope: CoroutineScope,
+    private val dispatcher: CoroutineDispatcher
+) {
+
+    /**
+     * Processes a new message without streaming.
+     *
+     * @param session The current chat session
+     * @param content The message content to send
+     * @param parentId The parent message ID (optional)
+     */
+    fun processNewMessage(session: ChatSession, content: String, parentId: Long?) {
+        scope.launch(dispatcher) {
+            inputStateManager.setSendingState(true)
+
+            try {
+                chatApi.processNewMessage(
+                    sessionId = session.id,
+                    request = ProcessNewMessageRequest(content = content, parentMessageId = parentId)
+                ).fold(
+                    ifLeft = { error ->
+                        errorHandler.handleMessageSendError(error, session.id, isStreaming = false)
+                    },
+                    ifRight = { newMessages ->
+                        // Add the new messages to the current session
+                        val newLeafId = newMessages.lastOrNull()?.id
+                        sessionStateManager.addMessagesToSession(newMessages, newLeafId)
+
+                        // Update the current branch leaf ID
+                        newLeafId?.let { sessionStateManager.updateCurrentBranchLeafId(it) }
+
+                        // Clear input and reply target on success
+                        inputStateManager.completeMessageSend()
+                    }
+                )
+            } finally {
+                inputStateManager.setSendingState(false)
+            }
+        }
+    }
+
+    /**
+     * Updates the content of an existing message.
+     *
+     * @param messageId The ID of the message to update
+     * @param newContent The new content for the message
+     * @param onSuccess Callback for successful update
+     * @param onError Callback for handling errors
+     */
+    fun updateMessageContent(
+        messageId: Long,
+        newContent: String,
+        onSuccess: () -> Unit = {},
+        onError: () -> Unit = {}
+    ) {
+        scope.launch(dispatcher) {
+            chatApi.updateMessageContent(
+                messageId = messageId,
+                request = UpdateMessageRequest(content = newContent)
+            ).fold(
+                ifLeft = { error ->
+                    errorHandler.handleMessageUpdateError(error, messageId)
+                    onError()
+                },
+                ifRight = { updatedMessage ->
+                    sessionStateManager.updateMessageInSession(updatedMessage)
+                    onSuccess()
+                }
+            )
+        }
+    }
+
+    /**
+     * Deletes a message and its children.
+     *
+     * @param messageId The ID of the message to delete
+     * @param onSuccess Callback for successful deletion
+     * @param onError Callback for handling errors
+     */
+    fun deleteMessage(
+        messageId: Long,
+        onSuccess: () -> Unit = {},
+        onError: () -> Unit = {}
+    ) {
+        scope.launch(dispatcher) {
+            chatApi.deleteMessage(messageId).fold(
+                ifLeft = { error ->
+                    errorHandler.handleMessageDeleteError(error, messageId)
+                    onError()
+                },
+                ifRight = {
+                    sessionStateManager.removeMessageFromSession(messageId)
+                    onSuccess()
+                }
+            )
+        }
+    }
+}
diff --git a/app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/chat/SessionStateManager.kt b/app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/chat/SessionStateManager.kt
new file mode 100644
index 0000000..93d2bcb
--- /dev/null
+++ b/app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/chat/SessionStateManager.kt
@@ -0,0 +1,241 @@
+package eu.torvian.chatbot.app.viewmodel.chat
+
+import eu.torvian.chatbot.app.domain.contracts.UiState
+import eu.torvian.chatbot.app.service.api.SessionApi
+import eu.torvian.chatbot.common.api.ApiError
+import eu.torvian.chatbot.common.models.ChatMessage
+import eu.torvian.chatbot.common.models.ChatSession
+import eu.torvian.chatbot.common.models.UpdateSessionLeafMessageRequest
+import kotlinx.coroutines.CoroutineDispatcher
+import kotlinx.coroutines.CoroutineScope
+import kotlinx.coroutines.flow.MutableStateFlow
+import kotlinx.coroutines.flow.StateFlow
+import kotlinx.coroutines.flow.asStateFlow
+import kotlinx.coroutines.launch
+import kotlinx.datetime.Clock
+
+/**
+ * Manages the state of the currently loaded chat session.
+ *
+ * This class is responsible for:
+ * - Loading and holding the current session state
+ * - Managing the current branch leaf message ID
+ * - Updating session properties like leaf message ID
+ * - Providing reactive state updates via StateFlow
+ *
+ * @property sessionApi The API client for session-related operations
+ * @property scope The coroutine scope for launching operations
+ * @property dispatcher The dispatcher for coroutine execution
+ * @property clock The clock for timestamping updates
+ */
+class SessionStateManager(
+    private val sessionApi: SessionApi,
+    private val scope: CoroutineScope,
+    private val dispatcher: CoroutineDispatcher,
+    private val clock: Clock = Clock.System
+) {
+
+    private val _sessionState = MutableStateFlow<UiState<ApiError, ChatSession>>(UiState.Idle)
+
+    /**
+     * The state of the currently loaded chat session, including all messages.
+     * When in Success state, provides the ChatSession object.
+     */
+    val sessionState: StateFlow<UiState<ApiError, ChatSession>> = _sessionState.asStateFlow()
+
+    private val _currentBranchLeafId = MutableStateFlow<Long?>(null)
+
+    /**
+     * The ID of the leaf message in the currently displayed thread branch.
+     * Changing this triggers the UI to show a different branch.
+     * Null if the session is empty or not loaded/successful.
+     */
+    val currentBranchLeafId: StateFlow<Long?> = _currentBranchLeafId.asStateFlow()
+
+    /**
+     * Gets the current session data if available and in Success state.
+     * @return The ChatSession data or null if not available
+     */
+    val currentSession: ChatSession?
+        get() = _sessionState.value.dataOrNull
+
+    /**
+     * Loads a session by ID and updates the state.
+     *
+     * @param sessionId The ID of the session to load
+     * @param onError Callback for handling errors during loading
+     */
+    fun loadSession(sessionId: Long, onError: (ApiError) -> Unit = {}) {
+        scope.launch(dispatcher) {
+            _sessionState.value = UiState.Loading
+
+            sessionApi.getSessionDetails(sessionId).fold(
+                ifLeft = { error ->
+                    _sessionState.value = UiState.Error(error)
+                    onError(error)
+                },
+                ifRight = { session ->
+                    _sessionState.value = UiState.Success(session)
+                    _currentBranchLeafId.value = session.currentLeafMessageId
+                }
+            )
+        }
+    }
+
+    /**
+     * Updates the current session state with new data.
+     *
+     * @param updatedSession The updated session data
+     */
+    fun updateSession(updatedSession: ChatSession) {
+        _sessionState.value = UiState.Success(updatedSession)
+    }
+
+    /**
+     * Updates the current branch leaf message ID locally.
+     *
+     * @param leafId The new leaf message ID
+     */
+    fun updateCurrentBranchLeafId(leafId: Long?) {
+        _currentBranchLeafId.value = leafId
+    }
+
+    /**
+     * Updates the session's leaf message ID both locally and on the backend.
+     *
+     * @param leafId The new leaf message ID
+     * @param onError Callback for handling errors during the update
+     * @param onSuccess Callback for successful update
+     */
+    fun updateSessionLeafMessage(
+        leafId: Long,
+        onError: (ApiError) -> Unit = {},
+        onSuccess: () -> Unit = {}
+    ) {
+        val session = currentSession ?: return
+
+        scope.launch(dispatcher) {
+            // Optimistically update UI state first for responsiveness
+            _currentBranchLeafId.value = leafId
+
+            // Persist the change to the backend
+            sessionApi.updateSessionLeafMessage(
+                session.id,
+                UpdateSessionLeafMessageRequest(leafId)
+            ).fold(
+                ifLeft = { error ->
+                    onError(error)
+                },
+                ifRight = {
+                    // Update the session object with the new leaf message ID
+                    val updatedSession = session.copy(currentLeafMessageId = leafId)
+                    _sessionState.value = UiState.Success(updatedSession)
+                    onSuccess()
+                }
+            )
+        }
+    }
+
+    /**
+     * Adds new messages to the current session.
+     *
+     * @param newMessages The messages to add
+     * @param newLeafId The new leaf message ID (optional)
+     */
+    fun addMessagesToSession(newMessages: List<ChatMessage>, newLeafId: Long? = null) {
+        val session = currentSession ?: return
+
+        val updatedMessages = session.messages + newMessages
+        val finalLeafId = newLeafId ?: newMessages.lastOrNull()?.id
+
+        val updatedSession = session.copy(
+            messages = updatedMessages,
+            currentLeafMessageId = finalLeafId,
+            updatedAt = clock.now()
+        )
+
+        _sessionState.value = UiState.Success(updatedSession)
+        _currentBranchLeafId.value = finalLeafId
+    }
+
+    /**
+     * Updates a specific message in the current session.
+     *
+     * @param updatedMessage The updated message
+     */
+    fun updateMessageInSession(updatedMessage: ChatMessage) {
+        val session = currentSession ?: return
+
+        val updatedMessages = session.messages.map { message ->
+            if (message.id == updatedMessage.id) updatedMessage else message
+        }
+
+        val updatedSession = session.copy(
+            messages = updatedMessages,
+            updatedAt = clock.now()
+        )
+
+        _sessionState.value = UiState.Success(updatedSession)
+    }
+
+    /**
+     * Removes a message and its children from the current session.
+     *
+     * @param messageId The ID of the message to remove
+     */
+    fun removeMessageFromSession(messageId: Long) {
+        val session = currentSession ?: return
+
+        // Find all messages to remove (the target message and all its descendants)
+        val messagesToRemove = findMessageAndDescendants(session.messages, messageId)
+        val updatedMessages = session.messages.filterNot { it.id in messagesToRemove }
+
+        val updatedSession = session.copy(
+            messages = updatedMessages,
+            updatedAt = clock.now()
+        )
+
+        _sessionState.value = UiState.Success(updatedSession)
+    }
+
+    /**
+     * Finds a message and all its descendants.
+     *
+     * @param messages All messages in the session
+     * @param messageId The ID of the root message to find descendants for
+     * @return Set of message IDs including the root and all descendants
+     */
+    private fun findMessageAndDescendants(
+        messages: List<ChatMessage>,
+        messageId: Long
+    ): Set<Long> {
+        val messageMap = messages.associateBy { it.id }
+        val toRemove = mutableSetOf<Long>()
+        val queue = mutableListOf(messageId)
+
+        while (queue.isNotEmpty()) {
+            val currentId = queue.removeFirst()
+            if (toRemove.add(currentId)) {
+                messageMap[currentId]?.childrenMessageIds?.let { children ->
+                    queue.addAll(children)
+                }
+            }
+        }
+
+        return toRemove
+    }
+
+    /**
+     * Retries loading the current session if it's in an error state.
+     *
+     * @param onError Callback for handling errors during retry
+     */
+    fun retryLoadSession(onError: (ApiError) -> Unit = {}) {
+        val currentState = _sessionState.value
+        if (currentState is UiState.Error) {
+            // We need to know which session to retry - this would need to be stored
+            // For now, we'll just reset to idle state
+            _sessionState.value = UiState.Idle
+        }
+    }
+}
diff --git a/app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/chat/StreamingMessageHandler.kt b/app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/chat/StreamingMessageHandler.kt
new file mode 100644
index 0000000..9e63d1d
--- /dev/null
+++ b/app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/chat/StreamingMessageHandler.kt
@@ -0,0 +1,204 @@
+package eu.torvian.chatbot.app.viewmodel.chat
+
+import eu.torvian.chatbot.app.service.api.ChatApi
+import eu.torvian.chatbot.app.utils.misc.kmpLogger
+import eu.torvian.chatbot.common.api.ApiError
+import eu.torvian.chatbot.common.models.ChatSession
+import eu.torvian.chatbot.common.models.ChatStreamEvent
+import eu.torvian.chatbot.common.models.ProcessNewMessageRequest
+import kotlinx.coroutines.CoroutineDispatcher
+import kotlinx.coroutines.CoroutineScope
+import kotlinx.coroutines.launch
+
+/**
+ * Handles streaming message processing operations.
+ *
+ * This class is responsible for:
+ * - Processing new messages with streaming responses
+ * - Managing streaming state during the process
+ * - Coordinating with state managers to update UI state
+ * - Handling streaming-specific errors and events
+ */
+class StreamingMessageHandler(
+    private val chatApi: ChatApi,
+    private val sessionStateManager: SessionStateManager,
+    private val inputStateManager: MessageInputStateManager,
+    private val streamingStateManager: StreamingStateManager,
+    private val errorHandler: ChatViewModelErrorHandler,
+    private val scope: CoroutineScope,
+    private val dispatcher: CoroutineDispatcher
+) {
+
+    private val logger = kmpLogger<StreamingMessageHandler>()
+
+    /**
+     * Processes a new message with streaming response.
+     *
+     * @param session The current chat session
+     * @param content The message content to send
+     * @param parentId The parent message ID (optional)
+     */
+    fun processStreamingMessage(session: ChatSession, content: String, parentId: Long?) {
+        scope.launch(dispatcher) {
+            inputStateManager.setSendingState(true)
+
+            try {
+                // Clear any previous streaming state
+                streamingStateManager.clearAllStreamingState()
+
+                chatApi.processNewMessageStreaming(
+                    sessionId = session.id,
+                    request = ProcessNewMessageRequest(content = content, parentMessageId = parentId)
+                ).collect { eitherUpdate ->
+                    eitherUpdate.fold(
+                        ifLeft = { error ->
+                            handleStreamingError(error, session.id)
+                        },
+                        ifRight = { chatUpdate ->
+                            handleStreamingEvent(chatUpdate, session)
+                        }
+                    )
+                }
+            } finally {
+                inputStateManager.setSendingState(false)
+            }
+        }
+    }
+
+    /**
+     * Handles individual streaming events.
+     *
+     * @param event The streaming event to handle
+     * @param session The current chat session
+     */
+    private suspend fun handleStreamingEvent(event: ChatStreamEvent, session: ChatSession) {
+        when (event) {
+            is ChatStreamEvent.UserMessageSaved -> {
+                handleUserMessageSaved(event)
+            }
+
+            is ChatStreamEvent.AssistantMessageStart -> {
+                handleAssistantMessageStart(event)
+            }
+
+            is ChatStreamEvent.AssistantMessageDelta -> {
+                handleAssistantMessageDelta(event)
+            }
+
+            is ChatStreamEvent.AssistantMessageEnd -> {
+                handleAssistantMessageEnd(event, session)
+            }
+
+            is ChatStreamEvent.ErrorOccurred -> {
+                handleStreamingEventError(event, session.id)
+            }
+
+            ChatStreamEvent.StreamCompleted -> {
+                handleStreamCompleted(session.id)
+            }
+        }
+    }
+
+    /**
+     * Handles the user message saved event.
+     */
+    private fun handleUserMessageSaved(event: ChatStreamEvent.UserMessageSaved) {
+        logger.debug("User message saved: ${event.message.id}")
+
+        // Store the user message in the temporary streaming state
+        streamingStateManager.setStreamingUserMessage(event.message)
+        sessionStateManager.updateCurrentBranchLeafId(event.message.id)
+
+        // Clear input and reply target after user message is confirmed
+        inputStateManager.completeMessageSend()
+    }
+
+    /**
+     * Handles the assistant message start event.
+     */
+    private fun handleAssistantMessageStart(event: ChatStreamEvent.AssistantMessageStart) {
+        logger.debug("Assistant message started: ${event.assistantMessage.id}")
+
+        // Use the assistant message directly from the update
+        sessionStateManager.updateCurrentBranchLeafId(event.assistantMessage.id)
+        streamingStateManager.setStreamingAssistantMessage(event.assistantMessage)
+    }
+
+    /**
+     * Handles assistant message delta events (incremental content updates).
+     */
+    private fun handleAssistantMessageDelta(event: ChatStreamEvent.AssistantMessageDelta) {
+        logger.debug("Assistant message delta: '${event.deltaContent}'")
+
+        // Update the streaming message content
+        streamingStateManager.updateStreamingAssistantContent(event.deltaContent)
+    }
+
+    /**
+     * Handles the assistant message end event.
+     */
+    private suspend fun handleAssistantMessageEnd(event: ChatStreamEvent.AssistantMessageEnd, session: ChatSession) {
+        logger.debug("Assistant message ended: ${event.finalAssistantMessage.id}")
+
+        // Update the session with the new messages
+        val newMessages = listOf(event.finalUserMessage, event.finalAssistantMessage)
+        val newLeafId = event.finalAssistantMessage.id
+
+        sessionStateManager.addMessagesToSession(newMessages, newLeafId)
+
+        // Clear streaming state
+        streamingStateManager.clearAllStreamingState()
+    }
+
+    /**
+     * Handles streaming-specific error events.
+     */
+    private suspend fun handleStreamingEventError(event: ChatStreamEvent.ErrorOccurred, sessionId: Long) {
+        logger.error("Streaming event error: ${event.error.message}")
+
+        // Clear streaming state on error
+        streamingStateManager.clearAllStreamingState()
+
+        // Handle the error through the error handler
+        errorHandler.handleStreamingError(event.error, sessionId, "event processing")
+    }
+
+    /**
+     * Handles stream completion.
+     */
+    private fun handleStreamCompleted(sessionId: Long) {
+        logger.info("Streaming completed for session $sessionId")
+        // Stream completion is handled by AssistantMessageEnd, so no additional action needed
+    }
+
+    /**
+     * Handles errors that occur during streaming setup or collection.
+     */
+    private suspend fun handleStreamingError(error: ApiError, sessionId: Long) {
+        logger.error("Streaming setup error: ${error.code} - ${error.message}")
+
+        // Clear any streaming state and emit error
+        streamingStateManager.clearAllStreamingState()
+
+        errorHandler.handleMessageSendError(error, sessionId, isStreaming = true)
+    }
+
+    /**
+     * Cancels any active streaming operation.
+     * This can be called when the user wants to stop streaming or when switching sessions.
+     */
+    fun cancelStreaming() {
+        logger.info("Cancelling active streaming operation")
+        streamingStateManager.clearAllStreamingState()
+        inputStateManager.setSendingState(false)
+    }
+
+    /**
+     * Checks if streaming is currently active.
+     *
+     * @return True if streaming is active, false otherwise
+     */
+    fun isStreamingActive(): Boolean {
+        return streamingStateManager.isStreaming
+    }
+}
diff --git a/app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/chat/StreamingStateManager.kt b/app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/chat/StreamingStateManager.kt
new file mode 100644
index 0000000..4744b40
--- /dev/null
+++ b/app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/chat/StreamingStateManager.kt
@@ -0,0 +1,116 @@
+package eu.torvian.chatbot.app.viewmodel.chat
+
+import eu.torvian.chatbot.common.models.ChatMessage
+import kotlinx.coroutines.flow.MutableStateFlow
+import kotlinx.coroutines.flow.StateFlow
+import kotlinx.coroutines.flow.asStateFlow
+
+/**
+ * Manages the state of streaming messages during chat interactions.
+ *
+ * This class is responsible for:
+ * - Holding temporary streaming user and assistant messages
+ * - Providing reactive state updates for streaming content
+ * - Managing the lifecycle of streaming messages
+ *
+ * During streaming:
+ * 1. User message is created and stored temporarily
+ * 2. Assistant message is created and updated incrementally
+ * 3. Both messages are cleared when streaming completes
+ */
+class StreamingStateManager {
+
+    private val _streamingUserMessage = MutableStateFlow<ChatMessage.UserMessage?>(null)
+
+    /**
+     * The temporary user message during streaming.
+     * This is set when the user message is saved but before the assistant response completes.
+     */
+    val streamingUserMessage: StateFlow<ChatMessage.UserMessage?> = _streamingUserMessage.asStateFlow()
+
+    private val _streamingAssistantMessage = MutableStateFlow<ChatMessage.AssistantMessage?>(null)
+
+    /**
+     * The actively streaming assistant message.
+     * This message's content is updated incrementally as the stream progresses.
+     */
+    val streamingAssistantMessage: StateFlow<ChatMessage.AssistantMessage?> = _streamingAssistantMessage.asStateFlow()
+
+    /**
+     * Indicates whether streaming is currently active.
+     */
+    val isStreaming: Boolean
+        get() = _streamingUserMessage.value != null || _streamingAssistantMessage.value != null
+
+    /**
+     * Sets the temporary user message during streaming.
+     *
+     * @param message The user message that was saved
+     */
+    fun setStreamingUserMessage(message: ChatMessage.UserMessage) {
+        _streamingUserMessage.value = message
+    }
+
+    /**
+     * Sets the initial assistant message for streaming.
+     *
+     * @param message The initial assistant message
+     */
+    fun setStreamingAssistantMessage(message: ChatMessage.AssistantMessage) {
+        _streamingAssistantMessage.value = message
+    }
+
+    /**
+     * Updates the content of the currently streaming assistant message.
+     *
+     * @param deltaContent The new content to append to the current message
+     */
+    fun updateStreamingAssistantContent(deltaContent: String) {
+        _streamingAssistantMessage.value?.let { currentMessage ->
+            _streamingAssistantMessage.value = currentMessage.copy(
+                content = currentMessage.content + deltaContent
+            )
+        }
+    }
+
+    /**
+     * Gets the current streaming assistant message content.
+     *
+     * @return The current content or empty string if no message is streaming
+     */
+    fun getCurrentStreamingContent(): String {
+        return _streamingAssistantMessage.value?.content ?: ""
+    }
+
+    /**
+     * Clears the streaming user message.
+     */
+    fun clearStreamingUserMessage() {
+        _streamingUserMessage.value = null
+    }
+
+    /**
+     * Clears the streaming assistant message.
+     */
+    fun clearStreamingAssistantMessage() {
+        _streamingAssistantMessage.value = null
+    }
+
+    /**
+     * Clears all streaming state.
+     * This should be called when streaming completes or encounters an error.
+     */
+    fun clearAllStreamingState() {
+        _streamingUserMessage.value = null
+        _streamingAssistantMessage.value = null
+    }
+
+    /**
+     * Gets both streaming messages as a list for display purposes.
+     *
+     * @return List containing non-null streaming messages in order (user first, then assistant)
+     */
+    fun getStreamingMessages(): List<ChatMessage> {
+        return listOfNotNull(_streamingUserMessage.value, _streamingAssistantMessage.value)
+    }
+}
diff --git a/app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/chat/ThreadBranchManager.kt b/app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/chat/ThreadBranchManager.kt
new file mode 100644
index 0000000..b0ba1df
--- /dev/null
+++ b/app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/chat/ThreadBranchManager.kt
@@ -0,0 +1,256 @@
+package eu.torvian.chatbot.app.viewmodel.chat
+
+import eu.torvian.chatbot.app.utils.misc.kmpLogger
+import eu.torvian.chatbot.common.models.ChatMessage
+
+/**
+ * Manages thread branch operations for chat message trees.
+ *
+ * This class is responsible for:
+ * - Building thread branches from flat message lists
+ * - Finding leaf messages in branches
+ * - Detecting and handling cycles in message trees
+ * - Providing thread navigation utilities
+ */
+class ThreadBranchManager {
+
+    private val logger = kmpLogger<ThreadBranchManager>()
+
+    /**
+     * Result of a thread branch operation.
+     */
+    sealed class ThreadBranchResult {
+        data class Success(val messages: List<ChatMessage>) : ThreadBranchResult()
+        data class Error(val message: String, val cause: ThreadBranchError) : ThreadBranchResult()
+    }
+
+    /**
+     * Types of errors that can occur during thread branch operations.
+     */
+    enum class ThreadBranchError {
+        LEAF_NOT_FOUND,
+        CYCLE_DETECTED,
+        BROKEN_LINK,
+        EMPTY_MESSAGE_LIST,
+        INVALID_MESSAGE_ID
+    }
+
+    /**
+     * Builds the list of messages for a specific branch.
+     *
+     * @param allMessages The flat list of all messages in the session
+     * @param leafId The ID of the desired leaf message for the branch
+     * @return ThreadBranchResult containing either the ordered messages or an error
+     */
+    fun buildThreadBranch(allMessages: List<ChatMessage>, leafId: Long?): ThreadBranchResult {
+        if (leafId == null) {
+            return ThreadBranchResult.Success(emptyList())
+        }
+
+        if (allMessages.isEmpty()) {
+            return ThreadBranchResult.Error(
+                "Cannot build branch: message list is empty",
+                ThreadBranchError.EMPTY_MESSAGE_LIST
+            )
+        }
+
+        val messageMap = allMessages.associateBy { it.id }
+        val branch = mutableListOf<ChatMessage>()
+        var currentMessageId: Long? = leafId
+        val visitedIds = mutableSetOf<Long>()
+
+        // Traverse upwards from the leaf to the root
+        while (currentMessageId != null) {
+            val message = messageMap[currentMessageId]
+            if (message == null) {
+                logger.error("Could not find message with ID $currentMessageId while building branch")
+                return ThreadBranchResult.Error(
+                    "Message with ID $currentMessageId not found",
+                    ThreadBranchError.BROKEN_LINK
+                )
+            }
+
+            if (!visitedIds.add(message.id)) {
+                logger.error("Cycle detected in message thread path during upward traversal at message ID: ${message.id}")
+                return ThreadBranchResult.Error(
+                    "Cycle detected at message ID: ${message.id}",
+                    ThreadBranchError.CYCLE_DETECTED
+                )
+            }
+
+            branch.add(message)
+            currentMessageId = message.parentMessageId
+        }
+
+        // Reverse to get root-to-leaf order
+        return ThreadBranchResult.Success(branch.reversed())
+    }
+
+    /**
+     * Finds the ultimate leaf message ID by traversing down the first child path from a given starting message ID.
+     *
+     * @param startMessageId The ID of the message to start the traversal from
+     * @param messageMap A map of all messages in the session for efficient lookup
+     * @return The ID of the leaf message found, or null if the startMessageId is invalid or an error occurs
+     */
+    fun findLeafOfBranch(startMessageId: Long, messageMap: Map<Long, ChatMessage>): Long? {
+        var currentMessage: ChatMessage? = messageMap[startMessageId]
+        if (currentMessage == null) {
+            logger.warn("Starting message for branch traversal not found: $startMessageId")
+            return null
+        }
+
+        var finalLeafId = startMessageId
+        val visitedIds = mutableSetOf<Long>()
+
+        while (currentMessage?.childrenMessageIds?.isNotEmpty() == true) {
+            if (!visitedIds.add(currentMessage.id)) {
+                logger.error("Cycle detected in message thread path at message ID: ${currentMessage.id}")
+                return null
+            }
+
+            // Select the first child to traverse down
+            val firstChildId = currentMessage.childrenMessageIds.first()
+            currentMessage = messageMap[firstChildId]
+            if (currentMessage == null) {
+                logger.warn("Child message $firstChildId not found during branch traversal. Using last valid message as leaf.")
+                break
+            }
+            finalLeafId = currentMessage.id
+        }
+
+        return finalLeafId
+    }
+
+    /**
+     * Finds all leaf messages in a message tree.
+     *
+     * @param messages The list of all messages
+     * @return List of message IDs that are leaf nodes (have no children)
+     */
+    fun findAllLeafMessages(messages: List<ChatMessage>): List<Long> {
+        return messages
+            .filter { it.childrenMessageIds.isEmpty() }
+            .map { it.id }
+    }
+
+    /**
+     * Finds all root messages in a message tree.
+     *
+     * @param messages The list of all messages
+     * @return List of message IDs that are root nodes (have no parent)
+     */
+    fun findAllRootMessages(messages: List<ChatMessage>): List<Long> {
+        return messages
+            .filter { it.parentMessageId == null }
+            .map { it.id }
+    }
+
+    /**
+     * Validates the integrity of a message tree structure.
+     *
+     * @param messages The list of all messages to validate
+     * @return List of validation errors found, empty if the tree is valid
+     */
+    fun validateMessageTree(messages: List<ChatMessage>): List<String> {
+        val errors = mutableListOf<String>()
+        val messageMap = messages.associateBy { it.id }
+
+        for (message in messages) {
+            // Check if parent exists (if specified)
+            message.parentMessageId?.let { parentId ->
+                if (messageMap[parentId] == null) {
+                    errors.add("Message ${message.id} references non-existent parent $parentId")
+                }
+            }
+
+            // Check if all children exist
+            for (childId in message.childrenMessageIds) {
+                if (messageMap[childId] == null) {
+                    errors.add("Message ${message.id} references non-existent child $childId")
+                }
+            }
+
+            // Check for self-reference
+            if (message.parentMessageId == message.id) {
+                errors.add("Message ${message.id} references itself as parent")
+            }
+            if (message.id in message.childrenMessageIds) {
+                errors.add("Message ${message.id} references itself as child")
+            }
+        }
+
+        // Check for orphaned messages (messages that claim to have a parent but the parent doesn't claim them as children)
+        for (message in messages) {
+            message.parentMessageId?.let { parentId ->
+                val parent = messageMap[parentId]
+                if (parent != null && message.id !in parent.childrenMessageIds) {
+                    errors.add("Message ${message.id} claims parent $parentId, but parent doesn't list it as child")
+                }
+            }
+        }
+
+        return errors
+    }
+
+    /**
+     * Gets the path from root to a specific message.
+     *
+     * @param targetMessageId The ID of the target message
+     * @param messageMap Map of all messages for efficient lookup
+     * @return List of message IDs from root to target, or empty list if path cannot be determined
+     */
+    fun getPathToMessage(targetMessageId: Long, messageMap: Map<Long, ChatMessage>): List<Long> {
+        val path = mutableListOf<Long>()
+        var currentMessageId: Long? = targetMessageId
+        val visitedIds = mutableSetOf<Long>()
+
+        while (currentMessageId != null) {
+            if (!visitedIds.add(currentMessageId)) {
+                logger.error("Cycle detected while finding path to message $targetMessageId")
+                return emptyList()
+            }
+
+            path.add(currentMessageId)
+            val message = messageMap[currentMessageId]
+            if (message == null) {
+                logger.error("Message $currentMessageId not found while building path")
+                return emptyList()
+            }
+
+            currentMessageId = message.parentMessageId
+        }
+
+        return path.reversed()
+    }
+
+    /**
+     * Checks if one message is an ancestor of another.
+     *
+     * @param ancestorId The potential ancestor message ID
+     * @param descendantId The potential descendant message ID
+     * @param messageMap Map of all messages for efficient lookup
+     * @return True if ancestorId is an ancestor of descendantId
+     */
+    fun isAncestor(ancestorId: Long, descendantId: Long, messageMap: Map<Long, ChatMessage>): Boolean {
+        if (ancestorId == descendantId) return false
+
+        var currentMessageId: Long? = descendantId
+        val visitedIds = mutableSetOf<Long>()
+
+        while (currentMessageId != null) {
+            if (!visitedIds.add(currentMessageId)) {
+                return false // Cycle detected
+            }
+
+            val message = messageMap[currentMessageId] ?: return false
+            currentMessageId = message.parentMessageId
+
+            if (currentMessageId == ancestorId) {
+                return true
+            }
+        }
+
+        return false
+    }
+}
diff --git a/app/src/desktopTest/kotlin/eu/torvian/chatbot/app/viewmodel/chat/SessionStateManagerTest.kt b/app/src/desktopTest/kotlin/eu/torvian/chatbot/app/viewmodel/chat/SessionStateManagerTest.kt
new file mode 100644
index 0000000..5ee3e35
--- /dev/null
+++ b/app/src/desktopTest/kotlin/eu/torvian/chatbot/app/viewmodel/chat/SessionStateManagerTest.kt
@@ -0,0 +1,246 @@
+package eu.torvian.chatbot.app.viewmodel.chat
+
+import eu.torvian.chatbot.app.domain.contracts.UiState
+import eu.torvian.chatbot.app.service.api.SessionApi
+import eu.torvian.chatbot.app.testutils.data.*
+import eu.torvian.chatbot.app.testutils.misc.TestClock
+import eu.torvian.chatbot.app.testutils.viewmodel.returnsDelayed
+import eu.torvian.chatbot.app.testutils.viewmodel.startCollecting
+import eu.torvian.chatbot.common.api.ApiError
+import eu.torvian.chatbot.common.models.ChatSession
+import eu.torvian.chatbot.common.models.UpdateSessionLeafMessageRequest
+import io.mockk.*
+import kotlinx.coroutines.ExperimentalCoroutinesApi
+import kotlinx.coroutines.test.*
+import kotlinx.datetime.LocalDateTime
+import kotlinx.datetime.TimeZone
+import kotlinx.datetime.toInstant
+import org.junit.jupiter.api.AfterEach
+import org.junit.jupiter.api.BeforeEach
+import org.junit.jupiter.api.Test
+import kotlin.test.assertEquals
+import kotlin.test.assertNull
+
+@ExperimentalCoroutinesApi
+class SessionStateManagerTest {
+    
+    private val testDispatcher = StandardTestDispatcher()
+    private lateinit var clock: TestClock
+    private val sessionApi: SessionApi = mockk()
+    
+    private lateinit var sessionStateManager: SessionStateManager
+    
+    // Collected emissions for assertions
+    private val collectedSessionStates = mutableListOf<UiState<ApiError, ChatSession>>()
+    private val collectedLeafIds = mutableListOf<Long?>()
+    
+    @BeforeEach
+    fun setUp() {
+        val initialTime = LocalDateTime(2023, 1, 1, 10, 0, 0).toInstant(TimeZone.UTC)
+        clock = TestClock(initialTime)
+        
+        sessionStateManager = SessionStateManager(
+            sessionApi = sessionApi,
+            scope = TestScope(testDispatcher),
+            dispatcher = testDispatcher,
+            clock = clock
+        )
+    }
+    
+    @AfterEach
+    fun tearDown() {
+        clearMocks(sessionApi)
+    }
+    
+    private fun TestScope.startCollecting() {
+        startCollecting(sessionStateManager.sessionState, collectedSessionStates)
+        startCollecting(sessionStateManager.currentBranchLeafId, collectedLeafIds)
+        advanceUntilIdle()
+        runCurrent()
+    }
+    
+    private fun clearCollected() {
+        collectedSessionStates.clear()
+        collectedLeafIds.clear()
+    }
+    
+    @Test
+    fun loadSession_success_updatesState() = runTest(testDispatcher) {
+        // Arrange
+        val session = mockSession2_threaded
+        coEvery { sessionApi.getSessionDetails(session.id) }.returnsDelayed(right(session))
+        
+        startCollecting()
+        clearCollected()
+        
+        // Act
+        sessionStateManager.loadSession(session.id)
+        advanceUntilIdle()
+        runCurrent()
+        
+        // Assert
+        assertEquals(2, collectedSessionStates.size)
+        assertEquals(UiState.Loading, collectedSessionStates[0])
+        assertEquals(UiState.Success(session), collectedSessionStates[1])
+        
+        assertEquals(1, collectedLeafIds.size)
+        assertEquals(session.currentLeafMessageId, collectedLeafIds[0])
+        
+        coVerify(exactly = 1) { sessionApi.getSessionDetails(session.id) }
+    }
+    
+    @Test
+    fun loadSession_error_updatesState() = runTest(testDispatcher) {
+        // Arrange
+        val sessionId = 123L
+        val error = genericApiError(statusCode = 404, code = "not-found", message = "Session not found")
+        coEvery { sessionApi.getSessionDetails(sessionId) }.returnsDelayed(left(error))
+        
+        var errorCallbackCalled = false
+        var capturedError: ApiError? = null
+        
+        startCollecting()
+        clearCollected()
+        
+        // Act
+        sessionStateManager.loadSession(sessionId) { apiError ->
+            errorCallbackCalled = true
+            capturedError = apiError
+        }
+        advanceUntilIdle()
+        runCurrent()
+        
+        // Assert
+        assertEquals(2, collectedSessionStates.size)
+        assertEquals(UiState.Loading, collectedSessionStates[0])
+        assertEquals(UiState.Error(error), collectedSessionStates[1])
+        
+        assertEquals(true, errorCallbackCalled)
+        assertEquals(error, capturedError)
+        
+        coVerify(exactly = 1) { sessionApi.getSessionDetails(sessionId) }
+    }
+    
+    @Test
+    fun updateSession_updatesState() = runTest(testDispatcher) {
+        // Arrange
+        val session = mockSession2_threaded
+        startCollecting()
+        clearCollected()
+        
+        // Act
+        sessionStateManager.updateSession(session)
+        advanceUntilIdle()
+        runCurrent()
+        
+        // Assert
+        assertEquals(1, collectedSessionStates.size)
+        assertEquals(UiState.Success(session), collectedSessionStates[0])
+    }
+    
+    @Test
+    fun updateCurrentBranchLeafId_updatesState() = runTest(testDispatcher) {
+        // Arrange
+        val leafId = 42L
+        startCollecting()
+        clearCollected()
+        
+        // Act
+        sessionStateManager.updateCurrentBranchLeafId(leafId)
+        advanceUntilIdle()
+        runCurrent()
+        
+        // Assert
+        assertEquals(1, collectedLeafIds.size)
+        assertEquals(leafId, collectedLeafIds[0])
+    }
+    
+    @Test
+    fun updateSessionLeafMessage_success_updatesStateAndCallsApi() = runTest(testDispatcher) {
+        // Arrange
+        val session = mockSession2_threaded
+        val newLeafId = 99L
+        
+        sessionStateManager.updateSession(session)
+        coEvery { 
+            sessionApi.updateSessionLeafMessage(session.id, UpdateSessionLeafMessageRequest(newLeafId)) 
+        }.returnsDelayed(right(Unit))
+        
+        startCollecting()
+        clearCollected()
+        
+        var successCallbackCalled = false
+        
+        // Act
+        sessionStateManager.updateSessionLeafMessage(
+            leafId = newLeafId,
+            onSuccess = { successCallbackCalled = true }
+        )
+        advanceUntilIdle()
+        runCurrent()
+        
+        // Assert
+        assertEquals(1, collectedLeafIds.size)
+        assertEquals(newLeafId, collectedLeafIds[0])
+        
+        assertEquals(1, collectedSessionStates.size)
+        val updatedSession = collectedSessionStates[0].dataOrNull
+        assertEquals(newLeafId, updatedSession?.currentLeafMessageId)
+        
+        assertEquals(true, successCallbackCalled)
+        
+        coVerify(exactly = 1) { 
+            sessionApi.updateSessionLeafMessage(session.id, UpdateSessionLeafMessageRequest(newLeafId)) 
+        }
+    }
+    
+    @Test
+    fun addMessagesToSession_updatesSessionWithNewMessages() = runTest(testDispatcher) {
+        // Arrange
+        val session = mockSession2_threaded
+        val newMessages = listOf(
+            userMessage(id = 100, sessionId = session.id, content = "New user message"),
+            assistantMessage(id = 101, sessionId = session.id, content = "New assistant message")
+        )
+        val newLeafId = 101L
+        
+        sessionStateManager.updateSession(session)
+        startCollecting()
+        clearCollected()
+        
+        // Act
+        sessionStateManager.addMessagesToSession(newMessages, newLeafId)
+        advanceUntilIdle()
+        runCurrent()
+        
+        // Assert
+        assertEquals(1, collectedSessionStates.size)
+        val updatedSession = collectedSessionStates[0].dataOrNull
+        assertEquals(session.messages.size + 2, updatedSession?.messages?.size)
+        assertEquals(newLeafId, updatedSession?.currentLeafMessageId)
+        
+        assertEquals(1, collectedLeafIds.size)
+        assertEquals(newLeafId, collectedLeafIds[0])
+    }
+    
+    @Test
+    fun currentSession_returnsCorrectValue() = runTest(testDispatcher) {
+        // Arrange
+        val session = mockSession2_threaded
+        
+        // Act & Assert - Initially null
+        assertNull(sessionStateManager.currentSession)
+        
+        // Update session
+        sessionStateManager.updateSession(session)
+        assertEquals(session, sessionStateManager.currentSession)
+        
+        // Set to error state
+        sessionStateManager.loadSession(999L) // Non-existent session
+        coEvery { sessionApi.getSessionDetails(999L) }.returnsDelayed(left(notFoundError()))
+        advanceUntilIdle()
+        runCurrent()
+        
+        assertNull(sessionStateManager.currentSession)
+    }
+}
diff --git a/app/src/desktopTest/kotlin/eu/torvian/chatbot/app/viewmodel/chat/ThreadBranchManagerTest.kt b/app/src/desktopTest/kotlin/eu/torvian/chatbot/app/viewmodel/chat/ThreadBranchManagerTest.kt
new file mode 100644
index 0000000..cd5ebfd
--- /dev/null
+++ b/app/src/desktopTest/kotlin/eu/torvian/chatbot/app/viewmodel/chat/ThreadBranchManagerTest.kt
@@ -0,0 +1,280 @@
+package eu.torvian.chatbot.app.viewmodel.chat
+
+import eu.torvian.chatbot.app.testutils.data.*
+import eu.torvian.chatbot.common.models.ChatMessage
+import org.junit.jupiter.api.BeforeEach
+import org.junit.jupiter.api.Test
+import kotlin.test.assertEquals
+import kotlin.test.assertNull
+import kotlin.test.assertTrue
+
+class ThreadBranchManagerTest {
+    
+    private lateinit var threadBranchManager: ThreadBranchManager
+    
+    @BeforeEach
+    fun setUp() {
+        threadBranchManager = ThreadBranchManager()
+    }
+    
+    @Test
+    fun buildThreadBranch_success_returnsCorrectBranch() {
+        // Arrange
+        val messages = allMockMessages
+        val leafId = m3.id // Branch: m1 -> m2 -> m3
+        
+        // Act
+        val result = threadBranchManager.buildThreadBranch(messages, leafId)
+        
+        // Assert
+        assertTrue(result is ThreadBranchManager.ThreadBranchResult.Success)
+        val branch = result.messages
+        assertEquals(3, branch.size)
+        assertEquals(m1.id, branch[0].id)
+        assertEquals(m2.id, branch[1].id)
+        assertEquals(m3.id, branch[2].id)
+    }
+    
+    @Test
+    fun buildThreadBranch_differentBranch_returnsCorrectBranch() {
+        // Arrange
+        val messages = allMockMessages
+        val leafId = m5.id // Branch: m1 -> m4 -> m5
+        
+        // Act
+        val result = threadBranchManager.buildThreadBranch(messages, leafId)
+        
+        // Assert
+        assertTrue(result is ThreadBranchManager.ThreadBranchResult.Success)
+        val branch = result.messages
+        assertEquals(3, branch.size)
+        assertEquals(m1.id, branch[0].id)
+        assertEquals(m4.id, branch[1].id)
+        assertEquals(m5.id, branch[2].id)
+    }
+    
+    @Test
+    fun buildThreadBranch_separateThread_returnsCorrectBranch() {
+        // Arrange
+        val messages = allMockMessages
+        val leafId = m7.id // Branch: m6 -> m7
+        
+        // Act
+        val result = threadBranchManager.buildThreadBranch(messages, leafId)
+        
+        // Assert
+        assertTrue(result is ThreadBranchManager.ThreadBranchResult.Success)
+        val branch = result.messages
+        assertEquals(2, branch.size)
+        assertEquals(m6.id, branch[0].id)
+        assertEquals(m7.id, branch[1].id)
+    }
+    
+    @Test
+    fun buildThreadBranch_nullLeafId_returnsEmptySuccess() {
+        // Arrange
+        val messages = allMockMessages
+        
+        // Act
+        val result = threadBranchManager.buildThreadBranch(messages, null)
+        
+        // Assert
+        assertTrue(result is ThreadBranchManager.ThreadBranchResult.Success)
+        assertTrue(result.messages.isEmpty())
+    }
+    
+    @Test
+    fun buildThreadBranch_emptyMessages_returnsError() {
+        // Arrange
+        val messages = emptyList<ChatMessage>()
+        val leafId = 1L
+        
+        // Act
+        val result = threadBranchManager.buildThreadBranch(messages, leafId)
+        
+        // Assert
+        assertTrue(result is ThreadBranchManager.ThreadBranchResult.Error)
+        assertEquals(ThreadBranchManager.ThreadBranchError.EMPTY_MESSAGE_LIST, result.cause)
+    }
+    
+    @Test
+    fun buildThreadBranch_missingMessage_returnsError() {
+        // Arrange
+        val messages = allMockMessages
+        val leafId = 999L // Non-existent message
+        
+        // Act
+        val result = threadBranchManager.buildThreadBranch(messages, leafId)
+        
+        // Assert
+        assertTrue(result is ThreadBranchManager.ThreadBranchResult.Error)
+        assertEquals(ThreadBranchManager.ThreadBranchError.BROKEN_LINK, result.cause)
+    }
+    
+    @Test
+    fun findLeafOfBranch_success_returnsLeafId() {
+        // Arrange
+        val messageMap = allMockMessages.associateBy { it.id }
+        val startMessageId = m1.id // Should find m3 as leaf (first branch: m1 -> m2 -> m3)
+
+        // Act
+        val result = threadBranchManager.findLeafOfBranch(startMessageId, messageMap)
+
+        // Assert
+        assertEquals(m3.id, result) // m3 is the leaf of the first branch (m1 -> m2 -> m3)
+    }
+    
+    @Test
+    fun findLeafOfBranch_alreadyLeaf_returnsSameId() {
+        // Arrange
+        val messageMap = allMockMessages.associateBy { it.id }
+        val startMessageId = m3.id // Already a leaf
+        
+        // Act
+        val result = threadBranchManager.findLeafOfBranch(startMessageId, messageMap)
+        
+        // Assert
+        assertEquals(m3.id, result)
+    }
+    
+    @Test
+    fun findLeafOfBranch_missingMessage_returnsNull() {
+        // Arrange
+        val messageMap = allMockMessages.associateBy { it.id }
+        val startMessageId = 999L // Non-existent message
+        
+        // Act
+        val result = threadBranchManager.findLeafOfBranch(startMessageId, messageMap)
+        
+        // Assert
+        assertNull(result)
+    }
+    
+    @Test
+    fun findAllLeafMessages_returnsCorrectLeaves() {
+        // Arrange
+        val messages = allMockMessages
+        
+        // Act
+        val result = threadBranchManager.findAllLeafMessages(messages)
+        
+        // Assert
+        assertEquals(3, result.size)
+        assertTrue(result.contains(m3.id)) // Leaf of first branch
+        assertTrue(result.contains(m5.id)) // Leaf of second branch
+        assertTrue(result.contains(m7.id)) // Leaf of third branch
+    }
+    
+    @Test
+    fun findAllRootMessages_returnsCorrectRoots() {
+        // Arrange
+        val messages = allMockMessages
+        
+        // Act
+        val result = threadBranchManager.findAllRootMessages(messages)
+        
+        // Assert
+        assertEquals(2, result.size)
+        assertTrue(result.contains(m1.id)) // Root of first thread
+        assertTrue(result.contains(m6.id)) // Root of second thread
+    }
+    
+    @Test
+    fun validateMessageTree_validTree_returnsNoErrors() {
+        // Arrange
+        val messages = allMockMessages
+        
+        // Act
+        val result = threadBranchManager.validateMessageTree(messages)
+        
+        // Assert
+        assertTrue(result.isEmpty())
+    }
+    
+    @Test
+    fun validateMessageTree_missingParent_returnsError() {
+        // Arrange
+        val messages = listOf(
+            userMessage(id = 1, sessionId = 100, content = "Child", parentMessageId = 999) // Parent doesn't exist
+        )
+        
+        // Act
+        val result = threadBranchManager.validateMessageTree(messages)
+        
+        // Assert
+        assertEquals(1, result.size)
+        assertTrue(result[0].contains("references non-existent parent 999"))
+    }
+    
+    @Test
+    fun validateMessageTree_missingChild_returnsError() {
+        // Arrange
+        val messages = listOf(
+            userMessage(id = 1, sessionId = 100, content = "Parent", childrenMessageIds = listOf(999)) // Child doesn't exist
+        )
+        
+        // Act
+        val result = threadBranchManager.validateMessageTree(messages)
+        
+        // Assert
+        assertEquals(1, result.size)
+        assertTrue(result[0].contains("references non-existent child 999"))
+    }
+    
+    @Test
+    fun getPathToMessage_success_returnsCorrectPath() {
+        // Arrange
+        val messageMap = allMockMessages.associateBy { it.id }
+        val targetMessageId = m3.id
+        
+        // Act
+        val result = threadBranchManager.getPathToMessage(targetMessageId, messageMap)
+        
+        // Assert
+        assertEquals(3, result.size)
+        assertEquals(m1.id, result[0])
+        assertEquals(m2.id, result[1])
+        assertEquals(m3.id, result[2])
+    }
+    
+    @Test
+    fun isAncestor_true_returnsTrue() {
+        // Arrange
+        val messageMap = allMockMessages.associateBy { it.id }
+        val ancestorId = m1.id
+        val descendantId = m3.id
+        
+        // Act
+        val result = threadBranchManager.isAncestor(ancestorId, descendantId, messageMap)
+        
+        // Assert
+        assertTrue(result)
+    }
+    
+    @Test
+    fun isAncestor_false_returnsFalse() {
+        // Arrange
+        val messageMap = allMockMessages.associateBy { it.id }
+        val ancestorId = m3.id
+        val descendantId = m1.id
+        
+        // Act
+        val result = threadBranchManager.isAncestor(ancestorId, descendantId, messageMap)
+        
+        // Assert
+        assertEquals(false, result)
+    }
+    
+    @Test
+    fun isAncestor_sameMessage_returnsFalse() {
+        // Arrange
+        val messageMap = allMockMessages.associateBy { it.id }
+        val messageId = m1.id
+        
+        // Act
+        val result = threadBranchManager.isAncestor(messageId, messageId, messageMap)
+        
+        // Assert
+        assertEquals(false, result)
+    }
+}
```

```kotlin
// Other files:


// file: C:/Users/<USER>/Documents/MyData/MyProjects/Chatbot/chatbot/app/src/commonMain/kotlin/eu/torvian/chatbot/app/domain/contracts/ChatAreaActions.kt
package eu.torvian.chatbot.app.domain.contracts

import eu.torvian.chatbot.common.models.ChatMessage

/**
 * Defines all UI actions that can be triggered from the main Chat Area.
 * (To be fully implemented in future PRs like 20, 21, etc.)
 */
interface ChatAreaActions {
    /**
     * Callback for when the user types in the message input field.
     * @param newText The new text content of the input field.
     */
    fun onUpdateInput(newText: String)

    /**
     * Callback for when the user sends a message.
     */
    fun onSendMessage()

    /**
     * Callback for when the user starts replying to a specific message.
     * @param message The message the user is replying to.
     */
    fun onStartReplyTo(message: ChatMessage)

    /**
     * Callback for when the user cancels the reply to a specific message.
     */
    fun onCancelReply()

    /**
     * Callback for when the user starts editing a specific message.
     * @param message The message the user is editing.
     */
    fun onStartEditing(message: ChatMessage)

    /**
     * Callback for when the user updates the content of the message being edited.
     * @param newText The new text content of the editing field.
     */
    fun onUpdateEditingContent(newText: String)

    /**
     * Callback for when the user saves the edited message content.
     */
    fun onSaveEditing()

    /**
     * Callback for when the user cancels the editing of a message.
     */
    fun onCancelEditing()

    /**
     * Callback for when the user deletes a specific message.
     * @param messageId The ID of the message to delete.
     */
    fun onDeleteMessage(messageId: Long)

    /**
     * Callback for when the user switches the displayed thread branch to a specific message.
     * @param messageId The ID of the message to make the new leaf of the displayed branch.
     */
    fun onSwitchBranchToMessage(messageId: Long)

    /**
     * Callback for when the user selects a specific LLM model for the session.
     * @param modelId The ID of the model to select, or null to clear selection.
     */
    fun onSelectModel(modelId: Long?)

    /**
     * Callback for when the user selects a specific settings profile for the session.
     * @param settingsId The ID of the settings profile to select, or null to clear selection.
     */
    fun onSelectSettings(settingsId: Long?)

    /**
     * Callback for when the user requests to retry loading the current chat session after a failure.
     */
    fun onRetryLoadingSession()

    // Will include copy actions (E2.S7, E3.S5) in future PRs
}

// file: C:/Users/<USER>/Documents/MyData/MyProjects/Chatbot/chatbot/app/src/commonMain/kotlin/eu/torvian/chatbot/app/domain/contracts/ChatAreaState.kt
package eu.torvian.chatbot.app.domain.contracts

import eu.torvian.chatbot.common.api.ApiError
        import eu.torvian.chatbot.common.models.ChatMessage
        import eu.torvian.chatbot.common.models.ChatSession

        /**
         * Encapsulates all UI state relevant to the main Chat Area.
         * (To be fully implemented in future PRs like 20, 21, etc.)
         *
         * @property sessionUiState The state of the currently loaded chat session, including all messages.
         * @property currentBranchLeafId The ID of the leaf message in the currently displayed thread branch.
         * @property displayedMessages The list of messages to display in the UI, representing the currently selected thread branch.
         * @property inputContent The current text content in the message input field.
         * @property replyTargetMessage The message the user is currently explicitly replying to via the Reply action.
         * @property editingMessage The message currently being edited (E3.S1, E3.S2).
         * @property editingContent The content of the message currently being edited (E3.S1, E3.S2).
         * @property isSendingMessage Indicates whether a message is currently in the process of being sent (E1.S3).
         */
        data class ChatAreaState(
    val sessionUiState: UiState<ApiError, ChatSession> = UiState.Idle,
    val currentBranchLeafId: Long? = null,
    val displayedMessages: List<ChatMessage> = emptyList(),
    val inputContent: String = "",
    val replyTargetMessage: ChatMessage? = null,
    val editingMessage: ChatMessage? = null,
    val editingContent: String = "",
    val isSendingMessage: Boolean = false

    // Will include model/settings selection states from ChatViewModel in future PRs
)

// file: C:/Users/<USER>/Documents/MyData/MyProjects/Chatbot/chatbot/app/src/commonMain/kotlin/eu/torvian/chatbot/app/domain/contracts/UiState.kt
package eu.torvian.chatbot.app.domain.contracts

import eu.torvian.chatbot.common.api.ApiError

        /**
         * Represents the different possible states of UI data loading and availability,
         * including an explicit Idle state.
         *
         * Used within UI State management classes to expose observable data to Compose UI.
         *
         * @param E The type of error that can occur (e.g., [ApiError]).
         * @param T The type of data that is loaded successfully.
         */
        sealed class UiState<out E, out T> {

    /**
     * The initial state, representing that no data loading has started or that
     * there is currently no data selected/available (e.g., no session loaded).
     */
    object Idle : UiState<Nothing, Nothing>()

    /**
     * The state when data is currently being fetched or processed.
     */
    object Loading : UiState<Nothing, Nothing>()

    /**
     * Represents a successful state with the loaded data.
     * @property data The successfully loaded data.
     */
    data class Success<out T>(val data: T) : UiState<Nothing, T>()

    /**
     * Represents an error state.
     * @property error The error information.
     */
    data class Error<out E>(val error: E) : UiState<E, Nothing>()

    // --- Helper functions ---

    val isLoading: Boolean get() = this is Loading
    val isSuccess: Boolean get() = this is Success
    val isError: Boolean get() = this is Error
    val isIdle: Boolean get() = this is Idle

    val dataOrNull: T?
        get() = (this as? Success)?.data

    val errorOrNull: E?
        get() = (this as? Error)?.error
}

// file: C:/Users/<USER>/Documents/MyData/MyProjects/Chatbot/chatbot/app/src/commonMain/kotlin/eu/torvian/chatbot/app/service/api/SessionApi.kt
package eu.torvian.chatbot.app.service.api

import arrow.core.Either
        import eu.torvian.chatbot.common.api.ApiError
        import eu.torvian.chatbot.common.models.ChatSession
        import eu.torvian.chatbot.common.models.ChatSessionSummary
        import eu.torvian.chatbot.common.models.CreateSessionRequest
        import eu.torvian.chatbot.common.models.UpdateSessionGroupRequest
        import eu.torvian.chatbot.common.models.UpdateSessionLeafMessageRequest
        import eu.torvian.chatbot.common.models.UpdateSessionModelRequest
        import eu.torvian.chatbot.common.models.UpdateSessionNameRequest
        import eu.torvian.chatbot.common.models.UpdateSessionSettingsRequest

/**
 * Frontend API interface for interacting with Chat Session-related endpoints.
 *
 * This interface defines the operations available for managing chat sessions,
 * including creation, retrieval, deletion, and updating session metadata
 * like name, group, model, settings, and leaf message. Implementations use the internal HTTP API.
 * All methods are suspend functions and return [Either<ApiError, T>] to explicitly
 * handle potential API errors.
 */
interface SessionApi {

    /**
     * Retrieves a list of summaries for all chat sessions.
     * Includes group affiliation for display in the session list.
     *
     * Corresponds to `GET /api/v1/sessions`.
     * (E2.S3)
     *
     * @return [Either.Right] containing a list of [ChatSessionSummary] on success,
     *         or [Either.Left] containing an [ApiError] on failure.
     */
    suspend fun getAllSessions(): Either<ApiError, List<ChatSessionSummary>>

    /**
     * Creates a new, empty chat session.
     *
     * Corresponds to `POST /api/v1/sessions`.
     * (E2.S1)
     *
     * @param request Optional request body containing a suggested name.
     * @return [Either.Right] containing the newly created [ChatSession] object on success,
     *         or [Either.Left] containing an [ApiError] on failure.
     */
    suspend fun createSession(request: CreateSessionRequest): Either<ApiError, ChatSession>

    /**
     * Retrieves the full details of a specific chat session, including all its messages.
     * Necessary for displaying the conversation history and threads.
     *
     * Corresponds to `GET /api/v1/sessions/{sessionId}`.
     * (E2.S4)
     *
     * @param sessionId The ID of the session to retrieve.
     * @return [Either.Right] containing the requested [ChatSession] object (with messages populated) on success,
     *         or [Either.Left] containing an [ApiError] on failure (e.g., not-found).
     */
    suspend fun getSessionDetails(sessionId: Long): Either<ApiError, ChatSession>

    /**
     * Deletes a chat session and all its associated messages.
     *
     * Corresponds to `DELETE /api/v1/sessions/{sessionId}`.
     * (E2.S6)
     *
     * @param sessionId The ID of the session to delete.
     * @return [Either.Right] with [Unit] on successful deletion (typically HTTP 204 No Content),
     *         or [Either.Left] containing an [ApiError] on failure.
     */
    suspend fun deleteSession(sessionId: Long): Either<ApiError, Unit>

    /**
     * Updates the name of a specific chat session.
     *
     * Corresponds to `PUT /api/v1/sessions/{sessionId}/name`.
     * (E2.S5)
     *
     * @param sessionId The ID of the session to rename.
     * @param request The request body containing the new name.
     * @return [Either.Right] with [Unit] on successful update (typically HTTP 200 OK with no body),
     *         or [Either.Left] containing an [ApiError] on failure.
     */
    suspend fun updateSessionName(sessionId: Long, request: UpdateSessionNameRequest): Either<ApiError, Unit>

    /**
     * Updates the currently selected LLM model for a specific chat session.
     *
     * Corresponds to `PUT /api/v1/sessions/{sessionId}/model`.
     * (E4.S7)
     *
     * @param sessionId The ID of the session.
     * @param request The request body containing the new optional model ID.
     * @return [Either.Right] with [Unit] on successful update,
     *         or [Either.Left] containing an [ApiError] on failure.
     */
    suspend fun updateSessionModel(sessionId: Long, request: UpdateSessionModelRequest): Either<ApiError, Unit>

    /**
     * Updates the currently selected settings profile for a specific chat session.
     *
     * Corresponds to `PUT /api/v1/sessions/{sessionId}/settings`.
     * (E4.S7)
     *
     * @param sessionId The ID of the session.
     * @param request The request body containing the new optional settings ID.
     * @return [Either.Right] with [Unit] on successful update,
     *         or [Either.Left] containing an [ApiError] on failure.
     */
    suspend fun updateSessionSettings(sessionId: Long, request: UpdateSessionSettingsRequest): Either<ApiError, Unit>

    /**
     * Sets the current "active" leaf message for a session, affecting which branch is displayed.
     *
     * Corresponds to `PUT /api/v1/sessions/{sessionId}/leafMessage`.
     * (E1.S5)
     *
     * @param sessionId The ID of the session.
     * @param request The request body containing the new optional leaf message ID.
     * @return [Either.Right] with [Unit] on successful update,
     *         or [Either.Left] containing an [ApiError] on failure.
     */
    suspend fun updateSessionLeafMessage(sessionId: Long, request: UpdateSessionLeafMessageRequest): Either<ApiError, Unit>

    /**
     * Assigns a specific chat session to a chat group, or ungroups it.
     *
     * Corresponds to `PUT /api/v1/sessions/{sessionId}/group`.
     * (E6.S1, E6.S7)
     *
     * @param sessionId The ID of the session to assign.
     * @param request The request body containing the new optional group ID.
     * @return [Either.Right] with [Unit] on successful update,
     *         or [Either.Left] containing an [ApiError] on failure.
     */
    suspend fun updateSessionGroup(sessionId: Long, request: UpdateSessionGroupRequest): Either<ApiError, Unit>
}

// file: C:/Users/<USER>/Documents/MyData/MyProjects/Chatbot/chatbot/app/src/commonMain/kotlin/eu/torvian/chatbot/app/service/api/SettingsApi.kt
package eu.torvian.chatbot.app.service.api

import arrow.core.Either
        import eu.torvian.chatbot.common.api.ApiError
        import eu.torvian.chatbot.common.models.ModelSettings

/**
 * Frontend API interface for interacting with Model Settings Profile-related endpoints.
 *
 * This interface defines the operations for managing LLM settings profiles.
 * Implementations use the internal HTTP API. All methods are suspend functions
 * and return [Either<ApiError, T>].
 */
interface SettingsApi {
    /**
     * Retrieves a list of all settings profiles associated with a specific LLM model.
     *
     * Corresponds to `GET /api/v1/models/{modelId}/settings`.
     * (E4.S5)
     *
     * @param modelId The ID of the model whose settings profiles to retrieve.
     * @return [Either.Right] containing a list of [ModelSettings] on success,
     *         or [Either.Left] containing an [ApiError] on failure (e.g., model not found).
     */
    suspend fun getSettingsByModelId(modelId: Long): Either<ApiError, List<ModelSettings>>

    /**
     * Creates a new settings profile.
     *
     * Corresponds to `POST /api/v1/settings`.
     * (E4.S5)
     *
     * @param settings The [ModelSettings] object to create.
     * @return [Either.Right] containing the newly created [ModelSettings] object on success,
     *         or [Either.Left] containing an [ApiError] on failure (e.g., invalid input, model not found).
     */
    suspend fun addModelSettings(settings: ModelSettings): Either<ApiError, ModelSettings>

    /**
     * Retrieves details for a specific settings profile.
     *
     * Corresponds to `GET /api/v1/settings/{settingsId}`.
     *
     * @param settingsId The ID of the settings profile to retrieve.
     * @return [Either.Right] containing the requested [ModelSettings] on success,
     *         or [Either.Left] containing an [ApiError] on failure (e.g., not found).
     */
    suspend fun getSettingsById(settingsId: Long): Either<ApiError, ModelSettings>

    /**
     * Updates the parameters of a specific settings profile.
     *
     * Corresponds to `PUT /api/v1/settings/{settingsId}`.
     * (E4.S6)
     *
     * @param settings The [ModelSettings] object with updated details. The ID must match the path.
     * @return [Either.Right] with [Unit] on successful update,
     *         or [Either.Left] containing an [ApiError] on failure (e.g., not found, invalid input).
     */
    suspend fun updateSettings(settings: ModelSettings): Either<ApiError, Unit>

    /**
     * Deletes a specific settings profile.
     * (E4.S5)
     *
     * @param settingsId The ID of the settings profile to delete.
     * @return [Either.Right] with [Unit] on successful deletion (typically HTTP 204 No Content),
     *         or [Either.Left] containing an [ApiError] on failure (e.g., not found).
     */
    suspend fun deleteSettings(settingsId: Long): Either<ApiError, Unit>
}

// file: C:/Users/<USER>/Documents/MyData/MyProjects/Chatbot/chatbot/app/src/commonMain/kotlin/eu/torvian/chatbot/app/service/misc/EventBus.kt
package eu.torvian.chatbot.app.service.misc

import eu.torvian.chatbot.app.domain.events.AppEvent
        import kotlinx.coroutines.flow.MutableSharedFlow
        import kotlinx.coroutines.flow.SharedFlow
        import kotlinx.coroutines.flow.asSharedFlow

/**
 * A generic EventBus for broadcasting application-wide, transient events.
 * It uses a SharedFlow to allow multiple collectors.
 */
class EventBus {
    private val _events = MutableSharedFlow<AppEvent>()
    val events: SharedFlow<AppEvent> = _events.asSharedFlow()

    /**
     * Emits a new application event.
     * @param event The event to emit.
     */
    suspend fun emitEvent(event: AppEvent) {
        _events.emit(event)
    }
}

// file: C:/Users/<USER>/Documents/MyData/MyProjects/Chatbot/chatbot/common/src/commonMain/kotlin/eu/torvian/chatbot/common/models/ChatMessage.kt
package eu.torvian.chatbot.common.models

import kotlinx.datetime.Instant
        import kotlinx.serialization.Serializable

        /**
         * Represents a single message within a chat session.
         *
         * Supports both user and assistant messages and includes threading information.
         * Uses a sealed class structure with a JSON class discriminator based on the 'role' field.
         * Used as a shared data model between frontend and backend API communication.
         *
         * @property id Unique identifier for the message (Database PK).
         * @property sessionId ID of the session this message belongs to (Database FK).
         * @property role The role of the message sender (e.g., "user", "assistant"). Used as the discriminator.
         * @property content The content of the message.
         * @property createdAt Timestamp when the message was created.
         * @property updatedAt Timestamp when the message was last updated (e.g., edited).
         * @property parentMessageId Optional ID of the parent message. Null for root messages of threads.
         * @property childrenMessageIds List of child message IDs. Empty for leaf messages.
         */
        @Serializable
        sealed class ChatMessage {
            abstract val id: Long
            abstract val sessionId: Long
            abstract val role: Role
            abstract val content: String
            abstract val createdAt: Instant
            abstract val updatedAt: Instant
            abstract val parentMessageId: Long?
            abstract val childrenMessageIds: List<Long>

            /**
             * Represents a message sent by the user.
             *
             * @property id Unique identifier for the message (Database PK).
             * @property sessionId ID of the session this message belongs to (Database FK).
             * @property content The content of the message.
             * @property createdAt Timestamp when the message was created.
             * @property updatedAt Timestamp when the message was last updated (e.g., edited).
             * @property parentMessageId Optional ID of the parent message. Null for root messages of threads.
             * @property childrenMessageIds List of child message IDs. Empty for leaf messages.
             */
            @Serializable
            data class UserMessage(
                override val id: Long,
                override val sessionId: Long,
                override val content: String,
                override val createdAt: Instant,
                override val updatedAt: Instant,
                override val parentMessageId: Long?,
                override val childrenMessageIds: List<Long> = emptyList()
            ) : ChatMessage() {
                override val role: Role = Role.USER
            }

            /**
             * Represents a message sent by the assistant (LLM).
             * Includes details about the model and settings used for generation.
             *
             * @property id Unique identifier for the message (Database PK).
             * @property sessionId ID of the session this message belongs to (Database FK).
             * @property content The content of the message.
             * @property createdAt Timestamp when the message was created.
             * @property updatedAt Timestamp when the message was last updated (e.g., edited).
             * @property parentMessageId Optional ID of the parent message. Null for root messages of threads.
             * @property childrenMessageIds List of child message IDs. Empty for leaf messages.
             * @property modelId ID of the LLM model used to generate this message.
             * @property settingsId ID of the settings profile used to generate this message.
             */
            @Serializable
            data class AssistantMessage(
                override val id: Long,
                override val sessionId: Long,
                override val content: String,
                override val createdAt: Instant,
                override val updatedAt: Instant,
                override val parentMessageId: Long?,
                override val childrenMessageIds: List<Long> = emptyList(),
                val modelId: Long?,
                val settingsId: Long?
            ) : ChatMessage() {
                override val role: Role = Role.ASSISTANT
            }

            /**
             * Enum defining the roles of message senders.
             */
            enum class Role {
                USER, ASSISTANT
            }
        }


// file: C:/Users/<USER>/Documents/MyData/MyProjects/Chatbot/chatbot/common/src/commonMain/kotlin/eu/torvian/chatbot/common/models/ChatSession.kt
package eu.torvian.chatbot.common.models

import kotlinx.datetime.Instant
        import kotlinx.serialization.Serializable

        /**
         * Represents a single chat session or conversation thread.
         * Used as a shared data model between frontend and backend.
         *
         * @property id Unique identifier for the session (Database PK).
         * @property name The name or title of the session.
         * @property createdAt Timestamp when the session was created.
         * @property updatedAt Timestamp when the session was last updated (e.g., message added).
         * @property groupId Optional ID referencing a parent group session.
         * @property currentModelId Optional ID of the currently selected LLM model for this session.
         * @property currentSettingsId Optional ID of the currently selected settings profile for this session.
         * @property currentLeafMessageId The current leaf message in the session, used for displaying the
         *                                correct branch in the UI. (Null only when no messages exist)
         * @property messages List of messages within this session (included when loading full details).
         */
        @Serializable
        data class ChatSession(
            val id: Long,
            val name: String,
            val createdAt: Instant,
            val updatedAt: Instant,
            val groupId: Long?,
            val currentModelId: Long?,
            val currentSettingsId: Long?,
            val currentLeafMessageId: Long?,
            val messages: List<ChatMessage> = emptyList()
        )



// file: C:/Users/<USER>/Documents/MyData/MyProjects/Chatbot/chatbot/app/src/commonMain/kotlin/eu/torvian/chatbot/app/compose/AppShell.kt
package eu.torvian.chatbot.app.compose

import androidx.compose.foundation.background
        import androidx.compose.foundation.layout.Box
        import androidx.compose.foundation.layout.fillMaxSize
        import androidx.compose.foundation.layout.fillMaxWidth
        import androidx.compose.foundation.layout.padding
        import androidx.compose.material3.*
        import androidx.compose.runtime.*
        import androidx.compose.ui.Modifier
        import androidx.compose.ui.graphics.Color
        import androidx.compose.ui.unit.dp
        import androidx.navigation.NavGraph.Companion.findStartDestination
        import androidx.navigation.compose.NavHost
        import androidx.navigation.compose.composable
        import androidx.navigation.compose.currentBackStackEntryAsState
        import androidx.navigation.compose.rememberNavController
        import eu.torvian.chatbot.app.compose.common.OverflowTooltipText
        import eu.torvian.chatbot.app.domain.events.GlobalError
        import eu.torvian.chatbot.app.domain.events.GlobalSuccess
        import eu.torvian.chatbot.app.domain.events.SnackbarInteractionEvent
        import eu.torvian.chatbot.app.domain.navigation.Chat
        import eu.torvian.chatbot.app.domain.navigation.Settings
        import eu.torvian.chatbot.app.generated.resources.Res
        import eu.torvian.chatbot.app.generated.resources.action_retry
        import eu.torvian.chatbot.app.generated.resources.app_name
        import eu.torvian.chatbot.app.service.misc.EventBus
        import eu.torvian.chatbot.app.viewmodel.ChatViewModel
        import eu.torvian.chatbot.app.viewmodel.SessionListViewModel
        import kotlinx.coroutines.launch
        import org.jetbrains.compose.resources.getString
        import org.jetbrains.compose.resources.stringResource
        import org.koin.compose.currentKoinScope
        import org.koin.compose.viewmodel.koinViewModel

        /**
         * The main application shell responsible for top-level layout and screen navigation.
         * Uses `Scaffold` for consistent Material Design structure and `NavHost` for navigation.
         * (E7.S2: Implement Base App Layout & ViewModel Integration with proper navigation)
         */
        @OptIn(ExperimentalMaterial3Api::class)
        @Composable
        fun AppShell() {
            val navController = rememberNavController()
            val navBackStackEntry by navController.currentBackStackEntryAsState()
            val currentRoute = navBackStackEntry?.destination?.route
            val eventBus: EventBus = currentKoinScope().get()
            val sessionListViewModel: SessionListViewModel = koinViewModel()
            val chatViewModel: ChatViewModel = koinViewModel()
            val snackbarHostState = remember { SnackbarHostState() }
            val scope = rememberCoroutineScope()

            // Collect events from EventBus and show them in Snackbar
            LaunchedEffect(eventBus) {
                eventBus.events.collect { event ->
                    if (event !is GlobalError && event !is GlobalSuccess) {
                        return@collect
                    }

                    // Launch a new coroutine to handle the Snackbar display
                    scope.launch {
                        val message: String
                        val actionLabel: String?
                        val duration: SnackbarDuration

                        when (event) {
                            is GlobalError -> {
                                message = event.message
                                actionLabel = if (event.isRetryable) getString(Res.string.action_retry) else null
                                duration = if (event.isRetryable) SnackbarDuration.Indefinite else SnackbarDuration.Long
                            }

                            is GlobalSuccess -> {
                                message = event.message
                                actionLabel = null
                                duration = SnackbarDuration.Short
                            }

                            else -> return@launch
                        }

                        // Show the Snackbar with custom visuals
                        val visuals = SnackbarVisualsWithError(
                            isError = event is GlobalError,
                            message = message,
                            actionLabel = actionLabel,
                            duration = duration
                        )
                        val result = snackbarHostState.showSnackbar(visuals)

                        // Emit a new event to communicate the Snackbar interaction back to ViewModels
                        eventBus.emitEvent(
                            SnackbarInteractionEvent(
                                originalAppEventId = event.eventId,
                                isActionPerformed = result == SnackbarResult.ActionPerformed
                            )
                        )
                    }
                }
            }

            // Initial loading of sessions and groups on app startup
            LaunchedEffect(Unit) {
                sessionListViewModel.loadSessionsAndGroups()
            }

            MaterialTheme(colorScheme = lightColorScheme()) {
                Scaffold(
                    topBar = {
                        CenterAlignedTopAppBar(
                            title = {
                                OverflowTooltipText(
                                    text = stringResource(Res.string.app_name),
                                )
                            },
                            actions = {
                                Button(
                                    onClick = {
                                        val targetRoute = if (currentRoute == Chat.name) Settings else Chat
                                        // This is the recommended pattern for "tab-like" navigation
                                        // It ensures that only one instance of a top-level screen exists
                                        // at any time, and its state is saved and restored.
                                        navController.navigate(targetRoute) {
                                            // Pop up to the start destination of the graph to
                                            // avoid building up a large stack of destinations
                                            // when selecting items from a bottom navigation bar.
                                            // This also ensures that the back button behavior
                                            // is predictable (usually exiting the app from the root tab).
                                            popUpTo(navController.graph.findStartDestination().id) {
                                                saveState = true // Save the state of the popped destinations
                                            }
                                            // Avoid multiple copies of the same destination when
                                            // reselecting the same item.
                                            launchSingleTop = true
                                            // Restore state when reselecting a previously selected item
                                            restoreState = true
                                        }
                                    }
                                ) {
                                    Text(if (currentRoute == Chat.name) "Settings" else "Chat")
                                }
                            },
                            modifier = Modifier.fillMaxWidth()
                                .background(MaterialTheme.colorScheme.primaryContainer)
                        )
                    },
                    snackbarHost = {
                        SnackbarHost(hostState = snackbarHostState) { data ->
                            val visualsWithError = data.visuals as? SnackbarVisualsWithError

                            val containerColor: Color
                            val contentColor: Color
                            val actionColor: Color
                            val actionContentColor: Color
                            val dismissActionContentColor: Color

                            if (visualsWithError?.isError == true) {
                                containerColor = MaterialTheme.colorScheme.errorContainer
                                contentColor = MaterialTheme.colorScheme.onErrorContainer
                                actionColor = MaterialTheme.colorScheme.error
                                actionContentColor = MaterialTheme.colorScheme.error
                                dismissActionContentColor = MaterialTheme.colorScheme.onErrorContainer
                            } else {
                                containerColor = MaterialTheme.colorScheme.inverseSurface
                                contentColor = MaterialTheme.colorScheme.inverseOnSurface
                                actionColor = MaterialTheme.colorScheme.inversePrimary
                                actionContentColor = MaterialTheme.colorScheme.inversePrimary
                                dismissActionContentColor = MaterialTheme.colorScheme.inverseOnSurface
                            }

                            Snackbar(
                                snackbarData = data,
                                modifier = Modifier.padding(12.dp),
                                containerColor = containerColor,
                                contentColor = contentColor,
                                actionColor = actionColor,
                                actionContentColor = actionContentColor,
                                dismissActionContentColor = dismissActionContentColor
                            )
                        }
                    }
                ) { paddingValues ->
                    Box(modifier = Modifier.fillMaxSize().padding(paddingValues)) {
                        NavHost(navController = navController, startDestination = Chat) {
                            composable<Chat> { // backStackEntry ->
                                // this can be used to extract arguments from the route:
//                        val route: Chat = backStackEntry.toRoute()
                                ChatScreen(sessionListViewModel, chatViewModel)
                            }
                            composable<Settings> {
                                SettingsScreen()
                            }
                        }
                    }
                }
            }
        }

/**
 * A custom SnackbarVisuals implementation with an additional `isError` flag.
 *
 * @param isError Whether the original event was an error (determines colors)
 * @param message The message to display in the Snackbar
 * @param actionLabel The label for the action button, if any
 * @param withDismissAction Whether to show a dismiss action (defaults to true)
 * @param duration The duration for which the Snackbar should be shown (defaults to SnackbarDuration.Short if no actionLabel, SnackbarDuration.Indefinite otherwise)
 */
data class SnackbarVisualsWithError(
    val isError: Boolean,
    override val message: String,
    override val actionLabel: String?,
    override val withDismissAction: Boolean = true,
    override val duration: SnackbarDuration =
        if (actionLabel != null) SnackbarDuration.Indefinite else SnackbarDuration.Short
) : SnackbarVisuals

// file: C:/Users/<USER>/Documents/MyData/MyProjects/Chatbot/chatbot/app/src/commonMain/kotlin/eu/torvian/chatbot/app/compose/ChatArea.kt
package eu.torvian.chatbot.app.compose

import androidx.compose.foundation.background
        import androidx.compose.foundation.hoverable
        import androidx.compose.foundation.interaction.MutableInteractionSource
        import androidx.compose.foundation.interaction.collectIsHoveredAsState
        import androidx.compose.foundation.layout.*
        import androidx.compose.foundation.lazy.LazyColumn
        import androidx.compose.foundation.lazy.items
        import androidx.compose.foundation.lazy.rememberLazyListState
        import androidx.compose.foundation.shape.RoundedCornerShape
        import androidx.compose.material.icons.Icons
        import androidx.compose.material.icons.automirrored.filled.ArrowBackIos
        import androidx.compose.material.icons.automirrored.filled.ArrowForwardIos
        import androidx.compose.material.icons.filled.ContentCopy
        import androidx.compose.material.icons.filled.Edit
        import androidx.compose.material.icons.filled.Refresh
        import androidx.compose.material3.Icon
        import androidx.compose.material3.IconButton
        import androidx.compose.material3.MaterialTheme
        import androidx.compose.material3.Text
        import androidx.compose.runtime.Composable
        import androidx.compose.runtime.getValue
        import androidx.compose.runtime.remember
        import androidx.compose.ui.Alignment
        import androidx.compose.ui.Modifier
        import androidx.compose.ui.draw.clip
        import androidx.compose.ui.text.font.FontWeight
        import androidx.compose.ui.text.style.TextAlign
        import androidx.compose.ui.unit.dp
        import eu.torvian.chatbot.app.compose.common.ErrorStateDisplay
        import eu.torvian.chatbot.app.compose.common.LoadingOverlay
        import eu.torvian.chatbot.app.compose.common.PlainTooltipBox
        import eu.torvian.chatbot.app.compose.common.ScrollbarWrapper
        import eu.torvian.chatbot.app.domain.contracts.ChatAreaActions
        import eu.torvian.chatbot.app.domain.contracts.ChatAreaState
        import eu.torvian.chatbot.app.domain.contracts.UiState
        import eu.torvian.chatbot.common.models.ChatMessage
        import eu.torvian.chatbot.common.models.ChatSession

        /**
         * Data class to hold branch navigation information for a message.
         *
         * @param alternativeBranchMessageIds The IDs of the alternative branch messages.
         * @param zeroBasedIndex The zero-based index of the current message in the alternative branch.
         * @param totalBranches The total number of branches for this message item.
         */
        private data class BranchNavigationData(
    val alternativeBranchMessageIds: List<Long>,
    val zeroBasedIndex: Int,
    val totalBranches: Int
) {
    val showNavigation: Boolean get() = totalBranches > 1
    val showPrev: Boolean get() = zeroBasedIndex > 0
    val showNext: Boolean get() = zeroBasedIndex < totalBranches - 1
}

/**
 * Data class to hold all potential actions that can be performed on a chat message.
 * Callbacks are nullable as not all actions might be implemented or applicable yet.
 * This pattern allows for clear API definition while maintaining flexibility for phased implementation.
 *
 * @param onSwitchBranchToMessage Callback to switch to a different thread branch.
 * @param onEditMessage Callback for when the user wants to edit a message.
 * @param onCopyMessage Callback for when the user wants to copy message content to clipboard.
 * @param onRegenerateMessage Callback for when the user wants to regenerate an assistant message.
 */
private data class MessageActions(
    val onSwitchBranchToMessage: (Long) -> Unit,
    val onEditMessage: ((ChatMessage) -> Unit)? = null,
    val onCopyMessage: ((ChatMessage) -> Unit)? = null,
    val onRegenerateMessage: ((ChatMessage) -> Unit)? = null
)

/**
 * Composable for the main chat message display area.
 * Handles displaying messages, loading/error states, and threading indicators.
 * (PR 20: Implement Chat Area UI (Message Display) (E1.S*))
 *
 * @param state The current UI state contract for the chat area.
 * @param actions The actions contract for the chat area.
 */
@Composable
fun ChatArea(
    state: ChatAreaState,
    actions: ChatAreaActions
) {
    Box(modifier = Modifier.fillMaxSize()) {
        when (state.sessionUiState) {
            UiState.Loading -> LoadingStateDisplay(modifier = Modifier.fillMaxSize())
            is UiState.Error -> ErrorStateDisplay(
                error = state.sessionUiState.error,
                onRetry = actions::onRetryLoadingSession,
                title = "Failed to load chat session",
                modifier = Modifier.align(Alignment.Center)
            )

            UiState.Idle -> IdleStateDisplay(modifier = Modifier.align(Alignment.Center))
            is UiState.Success -> SuccessStateDisplay(
                chatSession = state.sessionUiState.data,
                displayedMessages = state.displayedMessages,
                actions = actions, // Pass the full actions contract
                inputContent = state.inputContent,
                replyTargetMessage = state.replyTargetMessage,
                isSendingMessage = state.isSendingMessage
            )
        }
    }
}

/**
 * Displays a loading overlay.
 */
@Composable
private fun LoadingStateDisplay(modifier: Modifier = Modifier) {
    LoadingOverlay(modifier = modifier)
}

/**
 * Displays the idle state message when no session is selected.
 */
@Composable
private fun IdleStateDisplay(modifier: Modifier = Modifier) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Text(
            "Select a session from the left or create a new one.",
            style = MaterialTheme.typography.titleMedium,
            color = MaterialTheme.colorScheme.onBackground.copy(alpha = 0.7f),
            textAlign = TextAlign.Center
        )
        Spacer(Modifier.height(8.dp))
        Text(
            "Messages will appear here.",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onBackground.copy(alpha = 0.5f),
            textAlign = TextAlign.Center
        )
    }
}

/**
 * Displays the loaded chat session with messages.
 *
 * @param chatSession The current chat session data.
 * @param displayedMessages The list of messages to display.
 * @param actions The actions contract for the chat area, providing message-related callbacks.
 * @param inputContent The current text content in the message input field.
 * @param replyTargetMessage The message the user is currently explicitly replying to via the Reply action.
 * @param isSendingMessage Indicates whether a message is currently in the process of being sent.
 */
@Composable
private fun SuccessStateDisplay(
    chatSession: ChatSession,
    displayedMessages: List<ChatMessage>,
    actions: ChatAreaActions,
    inputContent: String,
    replyTargetMessage: ChatMessage?,
    isSendingMessage: Boolean
) {
    val allMessagesMap = remember(chatSession.messages) {
        chatSession.messages.associateBy { it.id }
    }
    val allRootMessageIds = remember(chatSession.messages) {
        chatSession.messages.filter { it.parentMessageId == null }
            .sortedBy { it.createdAt } // Ensure consistent order for roots
            .map { it.id }
    }

    // Prepare message actions to pass down
    val messageActions = remember(actions) {
        MessageActions(
            onSwitchBranchToMessage = actions::onSwitchBranchToMessage,
            // Future actions - uncomment and pass actions from `actions` contract when implemented:
            // onEditMessage = actions::onStartEditing,
            // onCopyMessage = actions::onCopyMessageContent,
            // onRegenerateMessage = actions::onRegenerateAssistantMessage
        )
    }

    // Create LazyListState for scrollbar integration
    val lazyListState = rememberLazyListState()

    Column(modifier = Modifier.fillMaxSize()) {
        ScrollbarWrapper(
            listState = lazyListState,
            modifier = Modifier.weight(1f) // Messages take up most space
        ) {
            LazyColumn(
                state = lazyListState,
                contentPadding = PaddingValues(bottom = 16.dp, end = 16.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(
                    items = displayedMessages,
                    key = { it.id } // Provide a key for efficient recomposition
                ) { message ->
                    val branchNavData = remember(message, allMessagesMap, allRootMessageIds) {
                        getBranchNavigationData(
                            message = message,
                            allMessagesMap = allMessagesMap,
                            allRootMessageIds = allRootMessageIds
                        )
                    }
                    MessageItem(
                        message = message,
                        branchNavigationData = branchNavData,
                        messageActions = messageActions
                    )
                }
            }
        }
        InputArea(
            inputContent = inputContent,
            onUpdateInput = actions::onUpdateInput,
            onSendMessage = actions::onSendMessage,
            replyTargetMessage = replyTargetMessage,
            onCancelReply = actions::onCancelReply,
            isSendingMessage = isSendingMessage,
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 8.dp) // Small padding between messages and input
        )
    }
}

/**
 * Helper function to determine the branch navigation data for a given message.
 *
 * @param message The current message being evaluated.
 * @param allMessagesMap A map of all messages in the session for efficient lookup.
 * @param allRootMessageIds A sorted list of all root message IDs in the session.
 * @return [BranchNavigationData] containing alternatives, current index, and total.
 */
private fun getBranchNavigationData(
    message: ChatMessage,
    allMessagesMap: Map<Long, ChatMessage>,
    allRootMessageIds: List<Long>
): BranchNavigationData {
    val alternativeBranchMessageIds: List<Long>

    if (message.parentMessageId != null) {
        // Case 1: Message has a parent -> alternatives are children of its parent
        val parentMessage = allMessagesMap[message.parentMessageId]
        if (parentMessage != null) {
            // Filter out deleted/non-existent children and sort for consistent ordering
            alternativeBranchMessageIds = parentMessage.childrenMessageIds
                .mapNotNull { allMessagesMap[it] }
                .sortedBy { it.createdAt } // Consistent order, e.g., by creation time
                .map { it.id }
        } else {
            // Parent not found, fall back to no navigation
            return BranchNavigationData(emptyList(), 0, 0)
        }
    } else {
        // Case 2: Message is a root message -> alternatives are other root messages (including itself)
        // allRootMessageIds is already sorted.
        alternativeBranchMessageIds = allRootMessageIds
    }

    if (alternativeBranchMessageIds.size <= 1) {
        // No alternatives if there's only one or zero options
        return BranchNavigationData(emptyList(), 0, 0)
    }

    // Determine the current index (zero-based)
    val indexOfCurrentAlternative: Int = alternativeBranchMessageIds.indexOf(message.id)

    if (indexOfCurrentAlternative == -1) {
        // This should theoretically not happen if `message` is part of `displayedMessages`
        // and `displayedMessages` correctly represents a branch from `alternativeBranchMessageIds`.
        // However, as a safeguard:
        return BranchNavigationData(emptyList(), 0, 0)
    }

    return BranchNavigationData(
        alternativeBranchMessageIds = alternativeBranchMessageIds,
        zeroBasedIndex = indexOfCurrentAlternative,
        totalBranches = alternativeBranchMessageIds.size
    )
}

/**
 * Composable for displaying a single chat message.
 *
 * @param message The ChatMessage to display.
 * @param branchNavigationData Pre-calculated data for branch navigation.
 * @param messageActions All actions that can be performed on this message.
 */
@Composable
private fun MessageItem(
    message: ChatMessage,
    branchNavigationData: BranchNavigationData,
    messageActions: MessageActions
) {
    val interactionSource = remember { MutableInteractionSource() }
    val hovered by interactionSource.collectIsHoveredAsState()

    val containerColor = when (message.role) {
        ChatMessage.Role.USER -> MaterialTheme.colorScheme.surfaceContainerLow
        ChatMessage.Role.ASSISTANT -> MaterialTheme.colorScheme.surfaceContainerHigh
    }
    val contentColor = when (message.role) {
        ChatMessage.Role.USER -> MaterialTheme.colorScheme.onSurfaceVariant
        ChatMessage.Role.ASSISTANT -> MaterialTheme.colorScheme.onSurface
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(8.dp))
            .background(containerColor)
            .hoverable(interactionSource)
            .padding(12.dp)
    ) {
        // Role and Name (e.g., "You:" or "AI:")
        Text(
            text = "${if (message.role == ChatMessage.Role.USER) "You" else "AI"}:",
            style = MaterialTheme.typography.labelSmall.copy(fontWeight = FontWeight.Bold),
            color = contentColor.copy(alpha = 0.8f)
        )
        Spacer(Modifier.height(4.dp))
        // Message Content
        Text(
            text = message.content,
            style = MaterialTheme.typography.bodyLarge,
            color = contentColor
        )

        // All message actions (branch navigation and future controls)
        Spacer(Modifier.height(8.dp))
        MessageActionRow(
            message = message,
            branchNavigationData = branchNavigationData,
            messageActions = messageActions,
            hovered = hovered,
            modifier = Modifier
                .fillMaxWidth()
                .height(24.dp) // Reserve the height for the action row
        )
    }
}

/**
 * Displays a row of action controls for a message.
 * General actions (Edit, Copy, Regenerate) are visible on hover, while branch navigation is always visible.
 *
 * @param message The [ChatMessage] for which controls are displayed (used for role-specific actions).
 * @param branchNavigationData Pre-calculated data for branch navigation.
 * @param messageActions All available actions for the message.
 * @param hovered Whether the parent [MessageItem] is currently hovered.
 * @param modifier Modifier to be applied to the component.
 */
@Composable
private fun MessageActionRow(
    message: ChatMessage,
    branchNavigationData: BranchNavigationData,
    messageActions: MessageActions,
    hovered: Boolean,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.SpaceBetween, // Pushes start actions to left, end actions to right
        verticalAlignment = Alignment.CenterVertically
    ) {
        // General actions aligned to the start - only visible on hover
        if (hovered) {
            GeneralMessageControls(message = message, messageActions = messageActions)
        } else {
            Spacer(Modifier.width(0.dp)) // Placeholder to maintain layout structure
        }

        // Branch Navigation Controls aligned to the end - always visible when navigation is available
        if (branchNavigationData.showNavigation) {
            BranchNavigationControls(
                branchNavigationData = branchNavigationData,
                onSwitchBranchToMessage = messageActions.onSwitchBranchToMessage
            )
        }
    }
}

/**
 * Displays the general action buttons for a message (e.g., Edit, Copy, Regenerate).
 *
 * @param message The [ChatMessage] for which controls are displayed.
 * @param messageActions All available actions for the message.
 * @param modifier Modifier to be applied to the component.
 */
@Composable
private fun GeneralMessageControls(
    message: ChatMessage,
    messageActions: MessageActions,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(4.dp) // Spacing between action icons
    ) {
        // Edit Button (User Message only)
        if (message.role == ChatMessage.Role.USER) {
            EditButton(message = message, onEditMessage = messageActions.onEditMessage)
        }

        // Copy Button (All messages)
        CopyButton(message = message, onCopyMessage = messageActions.onCopyMessage)

        // Regenerate Button (Assistant Message only)
        if (message.role == ChatMessage.Role.ASSISTANT) {
            RegenerateButton(message = message, onRegenerateMessage = messageActions.onRegenerateMessage)
        }
    }
}

/**
 * Displays the Edit message button.
 *
 * @param message The message to be edited.
 * @param onEditMessage Callback for the edit action, can be null if not implemented.
 */
@Composable
private fun EditButton(message: ChatMessage, onEditMessage: ((ChatMessage) -> Unit)?) {
    if (onEditMessage != null) {
        PlainTooltipBox(text = "Edit message") {
            IconButton(
                onClick = { onEditMessage(message) },
                modifier = Modifier.size(24.dp)
            ) {
                Icon(
                    Icons.Default.Edit,
                    contentDescription = "Edit message",
                    tint = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

/**
 * Displays the Copy message content button.
 *
 * @param message The message whose content is to be copied.
 * @param onCopyMessage Callback for the copy action, can be null if not implemented.
 */
@Composable
private fun CopyButton(message: ChatMessage, onCopyMessage: ((ChatMessage) -> Unit)?) {
    if (onCopyMessage != null) {
        PlainTooltipBox(text = "Copy message content") {
            IconButton(
                onClick = { onCopyMessage(message) },
                modifier = Modifier.size(24.dp)
            ) {
                Icon(
                    Icons.Default.ContentCopy,
                    contentDescription = "Copy message content",
                    tint = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

/**
 * Displays the Regenerate message button for assistant messages.
 *
 * @param message The assistant message to be regenerated.
 * @param onRegenerateMessage Callback for the regenerate action, can be null if not implemented.
 */
@Composable
private fun RegenerateButton(message: ChatMessage, onRegenerateMessage: ((ChatMessage) -> Unit)?) {
    if (onRegenerateMessage != null) {
        PlainTooltipBox(text = "Regenerate response") {
            IconButton(
                onClick = { onRegenerateMessage(message) },
                modifier = Modifier.size(24.dp)
            ) {
                Icon(
                    Icons.Default.Refresh,
                    contentDescription = "Regenerate response",
                    tint = MaterialTheme.colorScheme.primary
                )
            }
        }
    }
}

/**
 * Displays the branch navigation controls (previous, next, and count).
 *
 * @param branchNavigationData Pre-calculated data for branch navigation.
 * @param onSwitchBranchToMessage Callback to switch to a different thread branch.
 * @param modifier Modifier to be applied to the component.
 */
@Composable
private fun BranchNavigationControls(
    branchNavigationData: BranchNavigationData,
    onSwitchBranchToMessage: (Long) -> Unit,
    modifier: Modifier = Modifier
) {
    Row(modifier = modifier, verticalAlignment = Alignment.CenterVertically) {
        // Previous branch button '<'
        if (branchNavigationData.showPrev) {
            PlainTooltipBox(text = "Previous branch") {
                IconButton(
                    onClick = {
                        val prevIdx = branchNavigationData.zeroBasedIndex - 1
                        onSwitchBranchToMessage(branchNavigationData.alternativeBranchMessageIds[prevIdx])
                    },
                    modifier = Modifier.size(24.dp)
                ) {
                    Icon(
                        Icons.AutoMirrored.Filled.ArrowBackIos,
                        contentDescription = "Previous branch",
                        tint = MaterialTheme.colorScheme.primary
                    )
                }
            }
        } else {
            Spacer(Modifier.size(24.dp)) // Maintain spacing if button is hidden
        }

        // Current / Total count (1-based for UI)
        Text(
            text = "${branchNavigationData.zeroBasedIndex + 1} / ${branchNavigationData.totalBranches}",
            style = MaterialTheme.typography.labelSmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.padding(horizontal = 4.dp) // Small padding around text
        )

        // Next branch button '>'
        if (branchNavigationData.showNext) {
            PlainTooltipBox(text = "Next branch") {
                IconButton(
                    onClick = {
                        val nextIdx = branchNavigationData.zeroBasedIndex + 1
                        onSwitchBranchToMessage(branchNavigationData.alternativeBranchMessageIds[nextIdx])
                    },
                    modifier = Modifier.size(24.dp)
                ) {
                    Icon(
                        Icons.AutoMirrored.Filled.ArrowForwardIos,
                        contentDescription = "Next branch",
                        tint = MaterialTheme.colorScheme.primary
                    )
                }
            }
        } else {
            Spacer(Modifier.size(24.dp)) // Maintain spacing if button is hidden
        }
    }
}


// file: C:/Users/<USER>/Documents/MyData/MyProjects/Chatbot/chatbot/app/src/commonMain/kotlin/eu/torvian/chatbot/app/compose/ChatScreen.kt
package eu.torvian.chatbot.app.compose

import androidx.compose.runtime.Composable
        import androidx.compose.runtime.collectAsState
        import androidx.compose.runtime.getValue
        import androidx.compose.runtime.remember
        import eu.torvian.chatbot.app.domain.contracts.ChatAreaActions
        import eu.torvian.chatbot.app.domain.contracts.ChatAreaState
        import eu.torvian.chatbot.app.domain.contracts.SessionListActions
        import eu.torvian.chatbot.app.domain.contracts.SessionListState
        import eu.torvian.chatbot.app.viewmodel.ChatViewModel
        import eu.torvian.chatbot.app.viewmodel.SessionListViewModel
        import eu.torvian.chatbot.common.models.ChatGroup
        import eu.torvian.chatbot.common.models.ChatMessage
        import eu.torvian.chatbot.common.models.ChatSessionSummary
        import org.koin.compose.viewmodel.koinViewModel

        /**
         * A stateful wrapper Composable for the main chat interface.
         * This component is responsible for:
         * - Obtaining ViewModels via Koin.
         * - Collecting necessary state from these ViewModels.
         * - Managing specific ViewModel interactions for the chat feature.
         * - Constructing and passing stateless UI state and action contracts to [ChatScreenContent].
         * (E7.S2: Implementing the Stateful part of the Screen with internal ViewModel management)
         *
         * @param sessionListViewModel The ViewModel managing the session list state.
         * @param chatViewModel The ViewModel managing the active chat session state.
         */
        @Composable
        fun ChatScreen(
            sessionListViewModel: SessionListViewModel = koinViewModel(),
            chatViewModel: ChatViewModel = koinViewModel()
        ) {
            // --- Collect States for SessionListPanel ---
            val sessionListUiState by sessionListViewModel.listState.collectAsState()
            val selectedSessionId by sessionListViewModel.selectedSessionId.collectAsState()
            val isCreatingNewGroup by sessionListViewModel.isCreatingNewGroup.collectAsState()
            val newGroupNameInput by sessionListViewModel.newGroupNameInput.collectAsState()
            val editingGroup by sessionListViewModel.editingGroup.collectAsState()
            val editingGroupNameInput by sessionListViewModel.editingGroupNameInput.collectAsState()

            // --- Collect States for ChatArea ---
            val chatSessionUiState by chatViewModel.sessionState.collectAsState()
            val chatInputContent by chatViewModel.inputContent.collectAsState()
            val chatReplyTargetMessage by chatViewModel.replyTargetMessage.collectAsState()
            val chatEditingMessage by chatViewModel.editingMessage.collectAsState()
            val chatEditingContent by chatViewModel.editingContent.collectAsState()
            val chatCurrentBranchLeafId by chatViewModel.currentBranchLeafId.collectAsState()
            val chatDisplayedMessages by chatViewModel.displayedMessages.collectAsState()
            val chatIsSendingMessage by chatViewModel.isSendingMessage.collectAsState()

            // --- SessionListPanel Contract Construction ---
            val sessionListPanelUiState = remember(
                sessionListUiState, selectedSessionId, isCreatingNewGroup,
                newGroupNameInput, editingGroup, editingGroupNameInput
            ) {
                SessionListState(
                    listUiState = sessionListUiState,
                    selectedSessionId = selectedSessionId,
                    isCreatingNewGroup = isCreatingNewGroup,
                    newGroupNameInput = newGroupNameInput,
                    editingGroup = editingGroup,
                    editingGroupNameInput = editingGroupNameInput
                )
            }
            val sessionListPanelActions = remember(sessionListViewModel) {
                object : SessionListActions {
                    override fun onSessionSelected(sessionId: Long?) {
                        sessionListViewModel.selectSession(sessionId)
                        chatViewModel.loadSession(sessionId)
                    }

                    override fun onCreateNewSession(name: String?) = sessionListViewModel.createNewSession(name)
                    override fun onRenameSession(session: ChatSessionSummary, newName: String) =
                        sessionListViewModel.renameSession(session, newName)

                    override fun onDeleteSession(sessionId: Long) = sessionListViewModel.deleteSession(sessionId)
                    override fun onAssignSessionToGroup(sessionId: Long, groupId: Long?) =
                        sessionListViewModel.assignSessionToGroup(sessionId, groupId)

                    override fun onStartCreatingNewGroup() = sessionListViewModel.startCreatingNewGroup()
                    override fun onUpdateNewGroupNameInput(newText: String) =
                        sessionListViewModel.updateNewGroupNameInput(newText)

                    override fun onCreateNewGroup() = sessionListViewModel.createNewGroup()
                    override fun onCancelCreatingNewGroup() = sessionListViewModel.cancelCreatingNewGroup()
                    override fun onStartRenamingGroup(group: ChatGroup) = sessionListViewModel.startRenamingGroup(group)
                    override fun onUpdateEditingGroupNameInput(newText: String) =
                        sessionListViewModel.updateEditingGroupNameInput(newText)

                    override fun onSaveRenamedGroup() = sessionListViewModel.saveRenamedGroup()
                    override fun onCancelRenamingGroup() = sessionListViewModel.cancelRenamingGroup()
                    override fun onDeleteGroup(groupId: Long) = sessionListViewModel.deleteGroup(groupId)
                    override fun onRetryLoadingSessions() = sessionListViewModel.loadSessionsAndGroups()
                }
            }

            // --- ChatArea Contract Construction ---
            val chatAreaState = remember(
                chatSessionUiState, chatInputContent, chatReplyTargetMessage,
                chatEditingMessage, chatEditingContent, chatCurrentBranchLeafId, chatDisplayedMessages,
                chatIsSendingMessage
            ) {
                ChatAreaState(
                    sessionUiState = chatSessionUiState,
                    inputContent = chatInputContent,
                    replyTargetMessage = chatReplyTargetMessage,
                    editingMessage = chatEditingMessage,
                    editingContent = chatEditingContent,
                    currentBranchLeafId = chatCurrentBranchLeafId,
                    displayedMessages = chatDisplayedMessages,
                    isSendingMessage = chatIsSendingMessage
                )
            }
            val chatAreaActions = remember(chatViewModel, selectedSessionId) {
                object : ChatAreaActions {
                    override fun onUpdateInput(newText: String) = chatViewModel.updateInput(newText)
                    override fun onSendMessage() = chatViewModel.sendMessage()
                    override fun onStartReplyTo(message: ChatMessage) = chatViewModel.startReplyTo(message)
                    override fun onCancelReply() = chatViewModel.cancelReply()
                    override fun onStartEditing(message: ChatMessage) = chatViewModel.startEditing(message)
                    override fun onUpdateEditingContent(newText: String) = chatViewModel.updateEditingContent(newText)
                    override fun onSaveEditing() = chatViewModel.saveEditing()
                    override fun onCancelEditing() = chatViewModel.cancelEditing()
                    override fun onDeleteMessage(messageId: Long) = chatViewModel.deleteMessage(messageId)
                    override fun onSwitchBranchToMessage(messageId: Long) = chatViewModel.switchBranchToMessage(messageId)
                    override fun onSelectModel(modelId: Long?) = chatViewModel.selectModel(modelId)
                    override fun onSelectSettings(settingsId: Long?) = chatViewModel.selectSettings(settingsId)
                    override fun onRetryLoadingSession() {
                        selectedSessionId?.let { sessionId ->
                            chatViewModel.loadSession(sessionId, forceReload = true)
                        }
                    }
                }
            }

            // Pass all collected states and actions to the stateless ChatScreenContent
            ChatScreenContent(
                sessionListState = sessionListPanelUiState,
                sessionListActions = sessionListPanelActions,
                chatAreaState = chatAreaState,
                chatAreaActions = chatAreaActions
            )
        }


// file: C:/Users/<USER>/Documents/MyData/MyProjects/Chatbot/chatbot/app/src/commonMain/kotlin/eu/torvian/chatbot/app/compose/ChatScreenContent.kt
package eu.torvian.chatbot.app.compose

import androidx.compose.foundation.background
        import androidx.compose.foundation.layout.Box
        import androidx.compose.foundation.layout.Row
        import androidx.compose.foundation.layout.fillMaxSize
        import androidx.compose.foundation.layout.padding
        import androidx.compose.material3.MaterialTheme
        import androidx.compose.runtime.Composable
        import androidx.compose.ui.Modifier
        import androidx.compose.ui.unit.dp
        import eu.torvian.chatbot.app.domain.contracts.ChatAreaActions
        import eu.torvian.chatbot.app.domain.contracts.ChatAreaState
        import eu.torvian.chatbot.app.domain.contracts.SessionListActions
        import eu.torvian.chatbot.app.domain.contracts.SessionListState

        /**
         * Composable for the main chat interface's content, including the session list and the chat area.
         * This composable is now stateless, receiving all necessary data and callbacks via parameters.
         * (Part of E7.S2: Implement Base App Layout & ViewModel Integration - with State Hoisting)
         *
         * @param sessionListState The current UI state contract for the session list panel.
         * @param sessionListActions The actions contract for the session list panel.
         * @param chatAreaState The current UI state contract for the chat area.
         * @param chatAreaActions The actions contract for the chat area.
         */
        @Composable
        fun ChatScreenContent(
            sessionListState: SessionListState,
            sessionListActions: SessionListActions,
            chatAreaState: ChatAreaState,
            chatAreaActions: ChatAreaActions
        ) {
            Row(modifier = Modifier.fillMaxSize()) {
                Box(
                    modifier = Modifier
                        .weight(0.25f) // Fixed weight for Session List Panel
                        .fillMaxSize()
                        .background(MaterialTheme.colorScheme.surface)
                        .padding(16.dp),
                ) {
                    // PR 19: Session List Panel
                    SessionListPanel(
                        state = sessionListState,
                        actions = sessionListActions
                    )
                }
                Box(
                    modifier = Modifier
                        .weight(0.75f) // Fixed weight for Chat Area
                        .fillMaxSize()
                        .background(MaterialTheme.colorScheme.background)
                        .padding(16.dp)
                ) {
                    // PR 20: Implement Chat Area UI (Message Display)
                    ChatArea(
                        state = chatAreaState,
                        actions = chatAreaActions
                    )
                }
            }
        }


// file: C:/Users/<USER>/Documents/MyData/MyProjects/Chatbot/chatbot/app/src/commonMain/kotlin/eu/torvian/chatbot/app/compose/InputArea.kt
package eu.torvian.chatbot.app.compose

import androidx.compose.animation.AnimatedVisibility
        import androidx.compose.animation.expandVertically
        import androidx.compose.animation.shrinkVertically
        import androidx.compose.foundation.background
        import androidx.compose.foundation.layout.*
        import androidx.compose.foundation.shape.RoundedCornerShape
        import androidx.compose.foundation.text.KeyboardActions
        import androidx.compose.foundation.text.KeyboardOptions
        import androidx.compose.material.icons.Icons
        import androidx.compose.material.icons.automirrored.filled.Send
        import androidx.compose.material.icons.filled.Close
        import androidx.compose.material3.*
        import androidx.compose.runtime.Composable
        import androidx.compose.ui.Alignment
        import androidx.compose.ui.Modifier
        import androidx.compose.ui.draw.clip
        import androidx.compose.ui.graphics.Color
        import androidx.compose.ui.text.input.ImeAction
        import androidx.compose.ui.text.style.TextOverflow
        import androidx.compose.ui.unit.dp
        import eu.torvian.chatbot.app.compose.common.PlainTooltipBox // Import PlainTooltipBox
        import eu.torvian.chatbot.app.generated.resources.Res
        import eu.torvian.chatbot.app.generated.resources.cancel_reply_button_description
        import eu.torvian.chatbot.app.generated.resources.replying_to_prefix
        import eu.torvian.chatbot.app.generated.resources.send_message_button_description
        import eu.torvian.chatbot.common.models.ChatMessage
        import org.jetbrains.compose.resources.stringResource

        /**
         * Composable for the chat message input area. (PR 21: Implement Input Area UI (E1.S*, E1.S7))
         * Includes:
         * - Message input TextField
         * - Send button
         * - UI for replying to a specific message
         * - Loading indicator on send button (E1.S3)
         *
         * @param inputContent The current text content of the input field.
         * @param onUpdateInput Callback for when the input content changes.
         * @param onSendMessage Callback for when the send button is clicked or Enter is pressed.
         * @param replyTargetMessage The message being replied to, if any.
         * @param onCancelReply Callback to cancel the reply.
         * @param isSendingMessage Indicates if a message is currently being sent.
         * @param modifier Modifier to be applied to the component.
         */
        @Composable
        fun InputArea(
            inputContent: String,
            onUpdateInput: (String) -> Unit,
            onSendMessage: () -> Unit,
            replyTargetMessage: ChatMessage?,
            onCancelReply: () -> Unit,
            isSendingMessage: Boolean,
            modifier: Modifier = Modifier
        ) {
            val isSendButtonEnabled = inputContent.isNotBlank() && !isSendingMessage

            Column(modifier = modifier) {
                // Reply Target Display (E1.S7)
                AnimatedVisibility(
                    visible = replyTargetMessage != null,
                    enter = expandVertically(expandFrom = Alignment.Top),
                    exit = shrinkVertically(shrinkTowards = Alignment.Top)
                ) {
                    replyTargetMessage?.let { message ->
                        ReplyTargetBanner(message = message, onCancelReply = onCancelReply)
                    }
                }

                // Main Input Field and Send Button (E1.S1)
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 4.dp),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    TextField(
                        value = inputContent,
                        onValueChange = onUpdateInput,
                        modifier = Modifier
                            .weight(1f)
                            .defaultMinSize(minHeight = 48.dp), // Ensure a minimum height for the input
                        placeholder = { Text("Type a message...") },
                        singleLine = false, // Allow multiline input
                        maxLines = 5, // Limit input area growth
                        shape = RoundedCornerShape(24.dp), // Rounded corners for aesthetics
                        colors = TextFieldDefaults.colors(
                            focusedIndicatorColor = Color.Transparent,
                            unfocusedIndicatorColor = Color.Transparent,
                            disabledIndicatorColor = Color.Transparent,
                            errorIndicatorColor = Color.Transparent
                        ),
                        keyboardOptions = KeyboardOptions(imeAction = ImeAction.Send),
                        keyboardActions = KeyboardActions(
                            onSend = {
                                if (isSendButtonEnabled) {
                                    onSendMessage()
                                }
                            }
                        )
                    )

                    // Send Button or Loading Indicator
                    if (isSendingMessage) { // Loading indicator (E1.S3)
                        PlainTooltipBox(text = "Sending message...") {
                            FilledIconButton(
                                onClick = { }, // Can be used as stop action in future PRs
                                modifier = Modifier.size(48.dp),
                                enabled = true
                            ) {
                                CircularProgressIndicator(
                                    modifier = Modifier.size(24.dp),
                                    color = MaterialTheme.colorScheme.onPrimary,
                                    strokeWidth = 3.dp
                                )
                            }
                        }
                    } else { // Show Send Button (E1.S2)
                        PlainTooltipBox(text = stringResource(Res.string.send_message_button_description)) {
                            FilledIconButton(
                                onClick = onSendMessage,
                                modifier = Modifier.size(48.dp),
                                enabled = isSendButtonEnabled
                            ) {
                                Icon(
                                    Icons.AutoMirrored.Filled.Send,
                                    contentDescription = stringResource(Res.string.send_message_button_description),
                                    tint = MaterialTheme.colorScheme.onPrimary
                                )
                            }
                        }
                    }
                }
            }
        }

/**
 * Composable for displaying the banner indicating which message is being replied to. (E1.S7)
 *
 * @param message The message object being replied to.
 * @param onCancelReply Callback to cancel the reply.
 */
@Composable
private fun ReplyTargetBanner(
    message: ChatMessage,
    onCancelReply: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(8.dp))
            .background(MaterialTheme.colorScheme.surfaceContainerLow)
            .padding(horizontal = 12.dp, vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = "${stringResource(Res.string.replying_to_prefix)} \"${message.content}\"",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            modifier = Modifier.weight(1f)
        )
        Spacer(Modifier.width(8.dp))
        IconButton(
            onClick = onCancelReply,
            modifier = Modifier.size(24.dp)
        ) {
            Icon(
                Icons.Default.Close,
                contentDescription = stringResource(Res.string.cancel_reply_button_description),
                tint = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}



```