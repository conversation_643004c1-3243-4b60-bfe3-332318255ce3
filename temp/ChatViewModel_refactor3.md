# ChatViewModel Refactoring Plan

## Current Analysis

The `ChatViewModel` is currently a large, monolithic class (~700 lines) with multiple responsibilities:

1. **Session Management**: Loading, clearing, and updating session state
2. **Message Input Handling**: Managing input content and validation
3. **Message Operations**: Sending, editing, deleting messages
4. **Streaming Management**: Handling real-time message streaming
5. **Thread/Branch Navigation**: Managing displayed message branches
6. **Model/Settings Selection**: Updating session configuration
7. **Error Handling & Retry Logic**: Managing API errors and retry mechanisms
8. **Event Bus Integration**: Emitting and listening to global events
9. **State Derivation**: Computing derived states like `displayedMessages`

## Proposed Modular Architecture

### 1. Core ViewModel (ChatViewModel)
**Responsibilities:**
- Coordinate between different managers
- Expose consolidated state to UI
- Handle high-level user actions
- Manage ViewModel lifecycle

**Retained State:**
- `sessionState: StateFlow<UiState<ApiError, ChatSession>>`
- `displayedMessages: StateFlow<List<ChatMessage>>`
- `isSendingMessage: StateFlow<Boolean>`

### 2. SessionManager
**Responsibilities:**
- Session loading, clearing, and caching
- Session metadata updates (model, settings, group)
- Retry logic for failed session loads
- Branch/leaf message management

**State:**
- `_sessionState: MutableStateFlow<UiState<ApiError, ChatSession>>`
- `_currentBranchLeafId: MutableStateFlow<Long?>`
- `_lastFailedLoadEventId: MutableStateFlow<String?>`
- `_lastAttemptedSessionId: MutableStateFlow<Long?>`

**Methods:**
- `loadSession(sessionId: Long?, forceReload: Boolean = false)`
- `clearSession()`
- `switchBranchToMessage(targetMessageId: Long)`
- `selectModel(modelId: Long?)`
- `selectSettings(settingsId: Long?)`
- `handleRetryEvent(event: SnackbarInteractionEvent)`

### 3. MessageInputManager
**Responsibilities:**
- Input content management
- Reply target handling
- Input validation
- Message composition

**State:**
- `_inputContent: MutableStateFlow<String>`
- `_replyTargetMessage: MutableStateFlow<ChatMessage?>`

**Methods:**
- `updateInput(newText: String)`
- `startReplyTo(message: ChatMessage)`
- `cancelReply()`
- `validateInput(): Boolean`
- `composeMessage(): String`

### 4. MessageEditManager
**Responsibilities:**
- Message editing state
- Edit validation and saving
- Edit cancellation

**State:**
- `_editingMessage: MutableStateFlow<ChatMessage?>`
- `_editingContent: MutableStateFlow<String>`

**Methods:**
- `startEditing(message: ChatMessage)`
- `updateEditingContent(newText: String)`
- `saveEditing(): Flow<Either<ApiError, ChatMessage>>`
- `cancelEditing()`

### 5. StreamingMessageManager
**Responsibilities:**
- Streaming message state management
- Stream event processing
- Temporary message handling during streaming

**State:**
- `_streamingUserMessage: MutableStateFlow<ChatMessage.UserMessage?>`
- `_streamingAssistantMessage: MutableStateFlow<ChatMessage.AssistantMessage?>`

**Methods:**
- `handleStreamingMessage(session: ChatSession, content: String, parentId: Long?): Flow<Either<ApiError, Unit>>`
- `processStreamEvent(event: ChatStreamEvent)`
- `clearStreamingState()`

### 6. MessageOperationsManager
**Responsibilities:**
- Non-streaming message sending
- Message deletion
- Message operations coordination

**Methods:**
- `handleNonStreamingMessage(session: ChatSession, content: String, parentId: Long?): Either<ApiError, List<ChatMessage>>`
- `deleteMessage(messageId: Long): Either<ApiError, Unit>`

### 7. ThreadBranchManager
**Responsibilities:**
- Thread branch computation
- Message tree traversal
- Branch validation and cycle detection

**Methods:**
- `buildThreadBranch(allMessages: List<ChatMessage>, leafId: Long?): List<ChatMessage>`
- `findLeafOfBranch(startMessageId: Long, messageMap: Map<Long, ChatMessage>): Long?`
- `validateMessageTree(messages: List<ChatMessage>): Boolean`

## Implementation Strategy

### Phase 1: Extract Utility Classes
1. Create `ThreadBranchManager` - pure utility functions, no state
2. Create `MessageValidator` - input validation logic
3. Test these independently

### Phase 2: Extract State Managers
1. Create `SessionManager` with session-related state and operations
2. Create `MessageInputManager` for input handling
3. Create `MessageEditManager` for editing operations
4. Update `ChatViewModel` to use these managers

### Phase 3: Extract Complex Operations
1. Create `StreamingMessageManager` for streaming logic
2. Create `MessageOperationsManager` for message operations
3. Refactor `ChatViewModel` to coordinate between managers

### Phase 4: Optimize State Composition
1. Create composite state flows where beneficial
2. Optimize state derivation performance
3. Add comprehensive testing

## Benefits

### Maintainability
- **Single Responsibility**: Each class has a focused purpose
- **Easier Testing**: Smaller, focused units can be tested independently
- **Reduced Complexity**: Each class is easier to understand and modify

### Reusability
- **Modular Components**: Managers can potentially be reused in other contexts
- **Flexible Composition**: Different combinations of managers for different use cases

### Performance
- **Targeted Updates**: Only relevant state flows update when specific data changes
- **Memory Efficiency**: Unused managers can be garbage collected
- **Concurrent Operations**: Different managers can operate independently

### Developer Experience
- **Clearer Debugging**: Easier to trace issues to specific managers
- **Faster Development**: Developers can work on specific aspects without understanding the entire system
- **Better Code Reviews**: Smaller, focused changes are easier to review

## File Structure
```
app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/
├── ChatViewModel.kt (coordinator)
├── chat/
│   ├── SessionManager.kt
│   ├── MessageInputManager.kt
│   ├── MessageEditManager.kt
│   ├── StreamingMessageManager.kt
│   ├── MessageOperationsManager.kt
│   └── ThreadBranchManager.kt
└── utils/
    └── MessageValidator.kt
```

## Testing Strategy
- Unit tests for each manager independently
- Integration tests for manager interactions
- UI tests for the complete ChatViewModel behavior
- Performance tests for state flow efficiency

## Migration Path
1. Implement new managers alongside existing ChatViewModel
2. Gradually migrate functionality while maintaining backward compatibility
3. Update tests to cover new architecture
4. Remove old code once migration is complete
5. Optimize and refine the new architecture

This modular approach will make the codebase more maintainable, testable, and allow for easier future enhancements while preserving all existing functionality.
